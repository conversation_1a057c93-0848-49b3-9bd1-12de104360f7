-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set up basic tables for profile management
CREATE TABLE regions (
                         id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                         name TEXT NOT NULL,
                         code TEXT NOT NULL UNIQUE,
                         created_at TIMESTAMPTZ DEFAULT NOW(),
                         updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Profile descriptors (tags/attributes that can be applied to users or households)
CREATE TABLE profile_descriptors (
                                     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                     category TEXT NOT NULL,
                                     code TEXT NOT NULL UNIQUE,
                                     name TEXT NOT NULL,
                                     icon TEXT,
                                     created_at TIMESTAMPTZ DEFAULT NOW(),
                                     updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Household profiles
CREATE TABLE household_profiles (
                                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                    name TEXT NOT NULL,
                                    created_at TIMESTAMPTZ DEFAULT NOW(),
                                    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User profiles (linked to auth.users via Supabase Auth)
CREATE TABLE user_profiles (
                               id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
                               first_name TEXT NOT NULL,
                               last_name TEXT,
                               avatar_url TEXT,
                               household_id UUID REFERENCES household_profiles(id) ON DELETE SET NULL,
                               has_completed_onboarding BOOLEAN DEFAULT FALSE,
                               created_at TIMESTAMPTZ DEFAULT NOW(),
                               updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Junction table for household descriptors
CREATE TABLE household_descriptors (
                                       household_id UUID REFERENCES household_profiles(id) ON DELETE CASCADE,
                                       descriptor_id UUID REFERENCES profile_descriptors(id) ON DELETE CASCADE,
                                       created_at TIMESTAMPTZ DEFAULT NOW(),
                                       PRIMARY KEY (household_id, descriptor_id)
);

-- Junction table for user descriptors
CREATE TABLE user_descriptors (
                                  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
                                  descriptor_id UUID REFERENCES profile_descriptors(id) ON DELETE CASCADE,
                                  created_at TIMESTAMPTZ DEFAULT NOW(),
                                  PRIMARY KEY (user_id, descriptor_id)
);

-- Merchant groups
CREATE TABLE merchant_groups (
                                 id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                 name TEXT NOT NULL,
                                 description TEXT,
                                 online_only BOOLEAN NOT NULL DEFAULT FALSE,
                                 created_at TIMESTAMPTZ DEFAULT NOW(),
                                 updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Categories for merchant groups
CREATE TABLE merchant_categories (
                                     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                     name TEXT NOT NULL UNIQUE
);

-- Junction table for merchant_group categories
CREATE TABLE merchant_group_categories (
                                           merchant_group_id UUID REFERENCES merchant_groups(id) ON DELETE CASCADE,
                                           category_id UUID REFERENCES merchant_categories(id) ON DELETE CASCADE,
                                           PRIMARY KEY (merchant_group_id, category_id)
);

-- Merchant group logos
CREATE TABLE logos (
                       id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                       url TEXT NOT NULL,
                       width INTEGER,
                       height INTEGER,
                       merchant_group_id UUID REFERENCES merchant_groups(id) ON DELETE CASCADE,
                       created_at TIMESTAMPTZ DEFAULT NOW(),
                       updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Merchants (specific instances of merchant groups)
CREATE TABLE merchants (
                           id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                           name TEXT,
                           merchant_group_id UUID NOT NULL REFERENCES merchant_groups(id) ON DELETE CASCADE,
                           region_id UUID REFERENCES regions(id) ON DELETE SET NULL,
                           created_at TIMESTAMPTZ DEFAULT NOW(),
                           updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Merchant locations (optional)
CREATE TABLE merchant_locations (
                                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                    merchant_id UUID NOT NULL REFERENCES merchants(id) ON DELETE CASCADE,
                                    latitude DECIMAL(10, 8) NOT NULL,
                                    longitude DECIMAL(11, 8) NOT NULL,
                                    address TEXT,
                                    place_id TEXT,
                                    created_at TIMESTAMPTZ DEFAULT NOW(),
                                    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Products
CREATE TABLE products (
                          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                          name TEXT NOT NULL,
                          description TEXT NOT NULL,
                          merchant_id UUID NOT NULL REFERENCES merchants(id) ON DELETE CASCADE,
                          created_at TIMESTAMPTZ DEFAULT NOW(),
                          updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Product ideal_for tags
CREATE TABLE product_ideal_for (
                                   product_id UUID REFERENCES products(id) ON DELETE CASCADE,
                                   tag TEXT NOT NULL,
                                   PRIMARY KEY (product_id, tag)
);

create type BillingPeriods as enum (
  'Weekly',
  'Monthly',
  'Quarterly',
  'Annually'
);

-- Pricing tiers for products
CREATE TABLE pricing_tiers (
                               id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                               product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
                               name TEXT NOT NULL,
                               cost DECIMAL(10, 2) NOT NULL,
                               currency TEXT NOT NULL,
                               description TEXT,
                               billing_period BillingPeriod NOT NULL,
                               created_at TIMESTAMPTZ DEFAULT NOW(),
                               updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Pricing tier ideal_for tags
CREATE TABLE pricing_tier_ideal_for (
                                        pricing_tier_id UUID REFERENCES pricing_tiers(id) ON DELETE CASCADE,
                                        tag TEXT NOT NULL,
                                        PRIMARY KEY (pricing_tier_id, tag)
);

-- User subscriptions
CREATE TABLE user_subscriptions (
                                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
                                    household_id UUID REFERENCES household_profiles(id) ON DELETE SET NULL,
                                    profile_type TEXT NOT NULL CHECK (profile_type IN ('user', 'household')),
                                    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
                                    started_on TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                                    ended_on TIMESTAMPTZ,
                                    created_at TIMESTAMPTZ DEFAULT NOW(),
                                    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Profile questions
CREATE TABLE profile_questions (
                                   id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                   question_text TEXT NOT NULL,
                                   allow_multi_select BOOLEAN DEFAULT FALSE,
                                   created_at TIMESTAMPTZ DEFAULT NOW(),
                                   updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Profile question options
CREATE TABLE profile_question_options (
                                          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                          question_id UUID NOT NULL REFERENCES profile_questions(id) ON DELETE CASCADE,
                                          answer_text TEXT NOT NULL,
                                          created_at TIMESTAMPTZ DEFAULT NOW(),
                                          updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Junction table for question options and descriptors they grant
CREATE TABLE question_option_descriptors (
                                             option_id UUID REFERENCES profile_question_options(id) ON DELETE CASCADE,
                                             descriptor_id UUID REFERENCES profile_descriptors(id) ON DELETE CASCADE,
                                             PRIMARY KEY (option_id, descriptor_id)
);

-- Pending questions for users/households
CREATE TABLE pending_questions (
                                   id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                   question_id UUID NOT NULL REFERENCES profile_questions(id) ON DELETE CASCADE,
                                   target_type TEXT NOT NULL CHECK (target_type IN ('user', 'household')),
                                   target_id UUID NOT NULL,
                                   position INTEGER NOT NULL DEFAULT 0,
                                   created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Set up Row Level Security (RLS) policies

-- Enable RLS on all relevant tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE household_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_descriptors ENABLE ROW LEVEL SECURITY;
ALTER TABLE household_descriptors ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE pending_questions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_profiles
CREATE POLICY "Users can view their own profile"
  ON user_profiles FOR SELECT
                                  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
                                  USING (auth.uid() = id);

-- RLS Policies for household_profiles
CREATE POLICY "Users can view their household"
  ON household_profiles FOR SELECT
                                       USING (
                                       EXISTS (
                                       SELECT 1 FROM user_profiles
                                       WHERE user_profiles.household_id = household_profiles.id
                                       AND user_profiles.id = auth.uid()
                                       )
                                       );

CREATE POLICY "Users can update their household"
  ON household_profiles FOR UPDATE
                                       USING (
                                       EXISTS (
                                       SELECT 1 FROM user_profiles
                                       WHERE user_profiles.household_id = household_profiles.id
                                       AND user_profiles.id = auth.uid()
                                       )
                                       );

-- RLS Policies for user_subscriptions
CREATE POLICY "Users can view their own subscriptions"
  ON user_subscriptions FOR SELECT
                                       USING (user_id = auth.uid() OR
                                       (profile_type = 'household' AND
                                       EXISTS (
                                       SELECT 1 FROM user_profiles
                                       WHERE user_profiles.household_id = user_subscriptions.household_id
                                       AND user_profiles.id = auth.uid()
                                       )
                                       )
                                       );

-- Create indexes for performance
CREATE INDEX idx_user_profiles_household_id ON user_profiles(household_id);
CREATE INDEX idx_merchants_merchant_group_id ON merchants(merchant_group_id);
CREATE INDEX idx_products_merchant_id ON products(merchant_id);
CREATE INDEX idx_pricing_tiers_product_id ON pricing_tiers(product_id);
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_household_id ON user_subscriptions(household_id);
CREATE INDEX idx_user_subscriptions_product_id ON user_subscriptions(product_id);
CREATE INDEX idx_pending_questions_target ON pending_questions(target_type, target_id);

-- Create functions to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply the update_updated_at_column trigger to relevant tables
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE
    ON user_profiles FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_household_profiles_updated_at BEFORE UPDATE
    ON household_profiles FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_merchant_groups_updated_at BEFORE UPDATE
    ON merchant_groups FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_merchants_updated_at BEFORE UPDATE
    ON merchants FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE
    ON products FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_pricing_tiers_updated_at BEFORE UPDATE
    ON pricing_tiers FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE
    ON user_subscriptions FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Add example region data
INSERT INTO regions (name, code) VALUES
                                     ('Ireland', 'IE'),
                                     ('United Kingdom', 'UK'),
                                     ('United States', 'US');


-- First, modify the existing products table to remove the merchant_id
ALTER TABLE "public"."products"
DROP CONSTRAINT "products_merchant_id_fkey";

ALTER TABLE "public"."products"
DROP COLUMN "merchant_id";

-- Create a junction table for the many-to-many relationship
CREATE TABLE "public"."merchant_products" (
                                              "merchant_id" uuid NOT NULL,
                                              "product_id" uuid NOT NULL,
                                              "created_at" timestamp with time zone DEFAULT now(),
                                              "updated_at" timestamp with time zone DEFAULT now(),
                                              PRIMARY KEY ("merchant_id", "product_id"),
                                              FOREIGN KEY ("merchant_id") REFERENCES "public"."merchants"("id") ON DELETE CASCADE,
                                              FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX "idx_merchant_products_merchant_id" ON "public"."merchant_products" ("merchant_id");
CREATE INDEX "idx_merchant_products_product_id" ON "public"."merchant_products" ("product_id");

-- Add trigger for updating timestamp
CREATE TRIGGER update_merchant_products_updated_at
    BEFORE UPDATE ON public.merchant_products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT DELETE, INSERT, REFERENCES, SELECT, TRIGGER, TRUNCATE, UPDATE ON TABLE "public"."merchant_products" TO "anon";
GRANT DELETE, INSERT, REFERENCES, SELECT, TRIGGER, TRUNCATE, UPDATE ON TABLE "public"."merchant_products" TO "authenticated";
GRANT DELETE, INSERT, REFERENCES, SELECT, TRIGGER, TRUNCATE, UPDATE ON TABLE "public"."merchant_products" TO "service_role";


-- Add billing period to pricing tier
CREATE TYPE BillingPeriod AS ENUM ('Weekly', 'Monthly', 'Quarterly', 'Annually');

ALTER TABLE pricing_tiers
    ADD billing_period BillingPeriod

ALTER TABLE pricing_tiers
    ADD trial_period_days int

ALTER TABLE user_subscriptions
    DROP COLUMN household_id CASCADE;

ALTER TABLE user_subscriptions
    ADD COLUMN pricing_tier_id uuid,
ADD CONSTRAINT user_subscriptions_pricing_tier_id_fkey
FOREIGN KEY (pricing_tier_id) REFERENCES pricing_tiers(id) ON DELETE CASCADE;

ALTER TABLE user_subscriptions
    ADD bill_date varchar;

CREATE POLICY "Authenticated users can select their own subscriptions"
ON public.user_subscriptions
FOR SELECT
               TO authenticated
               USING ((select auth.uid()) = user_id);

CREATE POLICY "Authenticated users can insert their own subscriptions"
ON public.user_subscriptions
FOR INSERT
TO authenticated
WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Authenticated users can update their own subscriptions"
ON public.user_subscriptions
FOR UPDATE
                      TO authenticated
                      USING ((select auth.uid()) = user_id)
    WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Authenticated users can delete their own subscriptions"
ON public.user_subscriptions
FOR DELETE
TO authenticated
USING ((select auth.uid()) = user_id);

ALTER TABLE user_subscriptions
drop profile_type;

CREATE POLICY "Authenticated users can select their own subscriptions"
ON public.user_subscriptions
FOR SELECT
                    TO authenticated
                    USING ((select auth.uid()) = user_id);

CREATE POLICY "Authenticated users can insert their own subscriptions"
ON public.user_subscriptions
FOR INSERT
TO authenticated
WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Authenticated users can update their own subscriptions"
ON public.user_subscriptions
FOR UPDATE
                      TO authenticated
                      USING ((select auth.uid()) = user_id)
    WITH CHECK ((select auth.uid()) = user_id);

CREATE POLICY "Authenticated users can delete their own subscriptions"
ON public.user_subscriptions
FOR DELETE
TO authenticated
USING ((select auth.uid()) = user_id);

-- Notification targets
-- =====================================================
-- Supabase Notification Targets Table DDL Script
-- =====================================================
-- This script creates the notification_targets table for storing
-- push notification identifiers and device information.
--
-- Design decisions:
-- 1. Uses UUID for primary key for better distribution and security
-- 2. Foreign key to users table with CASCADE delete for data consistency
-- 3. Unique constraint on push_token to prevent duplicates
-- 4. Composite index on user_id + device_type for efficient queries
-- 5. Automatic timestamps for audit trail
-- 6. Device metadata stored as JSONB for flexibility
-- =====================================================

-- Create the notification_targets table if it doesn't exist
CREATE TABLE IF NOT EXISTS notification_targets (
                                                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    push_token TEXT NOT NULL UNIQUE,
    device_type TEXT NOT NULL CHECK (device_type IN ('ios', 'android', 'web')),
    device_info JSONB DEFAULT '{}',
    notification_preferences JSONB DEFAULT '{
        "questions": true,
        "recommendations": true,
        "other": true
    }',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_notification_at TIMESTAMP WITH TIME ZONE
                                                   );

CREATE INDEX IF NOT EXISTS idx_notification_targets_user_device
    ON notification_targets(user_id, device_type)
    WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_notification_targets_push_token
    ON notification_targets(push_token)
    WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_notification_targets_active
    ON notification_targets(is_active, created_at)
    WHERE is_active = true;

CREATE OR REPLACE FUNCTION update_notification_targets_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_notification_targets_updated_at ON notification_targets;
CREATE TRIGGER trigger_notification_targets_updated_at
    BEFORE UPDATE ON notification_targets
    FOR EACH ROW
    EXECUTE FUNCTION update_notification_targets_updated_at();

-- =====================================================
-- Row Level Security (RLS) Policies
-- =====================================================

ALTER TABLE notification_targets ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own notification targets
CREATE POLICY notification_targets_user_policy ON notification_targets
    FOR ALL USING (auth.uid() = user_id);

-- Policy: Allow service role to access all records (for admin operations)
CREATE POLICY notification_targets_service_policy ON notification_targets
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- Comments for Documentation
-- =====================================================

COMMENT ON TABLE notification_targets IS 'Stores push notification tokens and device information for users';
COMMENT ON COLUMN notification_targets.id IS 'Primary key using UUID';
COMMENT ON COLUMN notification_targets.user_id IS 'Foreign key to users table';
COMMENT ON COLUMN notification_targets.push_token IS 'Expo push notification token (unique)';
COMMENT ON COLUMN notification_targets.device_type IS 'Device platform: ios, android, or web';
COMMENT ON COLUMN notification_targets.device_info IS 'Flexible JSONB storage for device metadata';
COMMENT ON COLUMN notification_targets.notification_preferences IS 'User preferences for notification types';
COMMENT ON COLUMN notification_targets.is_active IS 'Whether this token is currently active';
COMMENT ON COLUMN notification_targets.created_at IS 'When the record was created';
COMMENT ON COLUMN notification_targets.updated_at IS 'When the record was last updated';
COMMENT ON COLUMN notification_targets.last_notification_at IS 'Timestamp of last successful notification';

-- Create notification_queue table for queuing notifications
CREATE TABLE notification_queue (
                                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                                    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
                                    target_device_id UUID REFERENCES notification_targets(id) ON DELETE CASCADE, -- NULL means all devices for user

    -- Notification content
                                    title TEXT NOT NULL,
                                    body TEXT NOT NULL,
                                    data JSONB DEFAULT '{}',
                                    channel_type TEXT NOT NULL DEFAULT 'other' CHECK (channel_type IN ('questions', 'recommendations', 'other')),

    -- Queue management
                                    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'expired')),
                                    priority INTEGER DEFAULT 0, -- Higher number = higher priority
                                    max_attempts INTEGER DEFAULT 3,
                                    attempt_count INTEGER DEFAULT 0,

    -- TTL and timing
                                    expires_at TIMESTAMPTZ NOT NULL,
                                    scheduled_for TIMESTAMPTZ DEFAULT NOW(), -- When to send (for delayed notifications)
                                    last_attempt_at TIMESTAMPTZ,

    -- Metadata
                                    created_at TIMESTAMPTZ DEFAULT NOW(),
                                    updated_at TIMESTAMPTZ DEFAULT NOW(),
                                    error_message TEXT
);

-- Create indexes for performance

CREATE INDEX idx_notification_queue_user_id ON notification_queue(user_id);
CREATE INDEX idx_notification_queue_status ON notification_queue(status);
CREATE INDEX idx_notification_queue_expires_at ON notification_queue(expires_at);
CREATE INDEX idx_notification_queue_scheduled_for ON notification_queue(scheduled_for);
CREATE INDEX idx_notification_queue_priority ON notification_queue(priority DESC, created_at ASC);
CREATE INDEX idx_notification_queue_pending ON notification_queue(status, scheduled_for, expires_at)
    WHERE status = 'pending';

-- Enable RLS
ALTER TABLE notification_queue ENABLE ROW LEVEL SECURITY;

-- RLS Policies for notification_queue
CREATE POLICY "Users can view their own queued notifications"
  ON notification_queue FOR SELECT
                                       USING (auth.uid() = user_id);

-- Service role can manage all queue operations
CREATE POLICY "Service role can manage notification queue"
  ON notification_queue FOR ALL
  USING (auth.role() = 'service_role');

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notification_queue_updated_at
    BEFORE UPDATE ON notification_queue
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();


-- Notification Queue Management Functions
-- Functions for managing the notification queue with TTL support

-- Function to queue a notification for a single device
CREATE OR REPLACE FUNCTION queue_notification_for_device(
  p_user_id UUID,
  p_target_device_id UUID,
  p_title TEXT,
  p_body TEXT,
  p_data JSONB DEFAULT '{}',
  p_channel_type TEXT DEFAULT 'other',
  p_ttl_minutes INTEGER DEFAULT 60,
  p_priority INTEGER DEFAULT 0,
  p_scheduled_for TIMESTAMPTZ DEFAULT NOW()
)
RETURNS UUID AS $$
DECLARE
notification_id UUID;
  expires_at TIMESTAMPTZ;
BEGIN
  -- Calculate expiration time
  expires_at := p_scheduled_for + (p_ttl_minutes || ' minutes')::INTERVAL;

  -- Validate that the target device belongs to the user
  IF NOT EXISTS (
    SELECT 1 FROM notification_targets
    WHERE id = p_target_device_id
    AND user_id = p_user_id
    AND is_active = true
  ) THEN
    RAISE EXCEPTION 'Invalid target device for user';
END IF;

  -- Insert notification into queue
INSERT INTO notification_queue (
    user_id,
    target_device_id,
    title,
    body,
    data,
    channel_type,
    priority,
    expires_at,
    scheduled_for
) VALUES (
             p_user_id,
             p_target_device_id,
             p_title,
             p_body,
             p_data,
             p_channel_type,
             p_priority,
             expires_at,
             p_scheduled_for
         ) RETURNING id INTO notification_id;

RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to queue a notification for all devices of a user
CREATE OR REPLACE FUNCTION queue_notification_for_user(
  p_user_id UUID,
  p_title TEXT,
  p_body TEXT,
  p_data JSONB DEFAULT '{}',
  p_channel_type TEXT DEFAULT 'other',
  p_ttl_minutes INTEGER DEFAULT 60,
  p_priority INTEGER DEFAULT 0,
  p_scheduled_for TIMESTAMPTZ DEFAULT NOW()
)
RETURNS UUID[] AS $$
DECLARE
notification_ids UUID[] := '{}';
  device_record RECORD;
  notification_id UUID;
  expires_at TIMESTAMPTZ;
BEGIN
  -- Calculate expiration time
  expires_at := p_scheduled_for + (p_ttl_minutes || ' minutes')::INTERVAL;

  -- Get all active devices for the user that have the channel enabled
FOR device_record IN
SELECT id
FROM notification_targets
WHERE user_id = p_user_id
  AND is_active = true
  AND (notification_preferences->p_channel_type)::boolean = true
  LOOP
-- Insert notification for each device
INSERT INTO notification_queue (
    user_id,
    target_device_id,
    title,
    body,
    data,
    channel_type,
    priority,
    expires_at,
    scheduled_for
) VALUES (
    p_user_id,
    device_record.id,
    p_title,
    p_body,
    p_data,
    p_channel_type,
    p_priority,
    expires_at,
    p_scheduled_for
    ) RETURNING id INTO notification_id;

notification_ids := array_append(notification_ids, notification_id);
END LOOP;

RETURN notification_ids;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pending notifications ready to be sent
CREATE OR REPLACE FUNCTION get_pending_notifications(
  p_limit INTEGER DEFAULT 100
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  target_device_id UUID,
  push_token TEXT,
  device_type TEXT,
  title TEXT,
  body TEXT,
  data JSONB,
  channel_type TEXT,
  priority INTEGER,
  attempt_count INTEGER,
  max_attempts INTEGER
) AS $$
BEGIN
RETURN QUERY
SELECT
    nq.id,
    nq.user_id,
    nq.target_device_id,
    nt.push_token,
    nt.device_type,
    nq.title,
    nq.body,
    nq.data,
    nq.channel_type,
    nq.priority,
    nq.attempt_count,
    nq.max_attempts
FROM notification_queue nq
         JOIN notification_targets nt ON nq.target_device_id = nt.id
WHERE nq.status = 'pending'
  AND nq.scheduled_for <= NOW()
  AND nq.expires_at > NOW()
  AND nt.is_active = true
ORDER BY nq.priority DESC, nq.created_at ASC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as being processed
CREATE OR REPLACE FUNCTION mark_notification_processing(
  p_notification_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
UPDATE notification_queue
SET
    status = 'processing',
    last_attempt_at = NOW(),
    attempt_count = attempt_count + 1
WHERE id = p_notification_id
  AND status = 'pending'
  AND expires_at > NOW();

RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as successfully sent (removes from queue)
CREATE OR REPLACE FUNCTION mark_notification_sent(
  p_notification_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Update the last_notification_at timestamp on the target device
UPDATE notification_targets
SET last_notification_at = NOW()
WHERE id = (
    SELECT target_device_id
    FROM notification_queue
    WHERE id = p_notification_id
);

-- Remove the notification from the queue
DELETE FROM notification_queue
WHERE id = p_notification_id;

RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as failed
CREATE OR REPLACE FUNCTION mark_notification_failed(
  p_notification_id UUID,
  p_error_message TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
current_attempts INTEGER;
  max_attempts INTEGER;
BEGIN
  -- Get current attempt count and max attempts
SELECT attempt_count, max_attempts
INTO current_attempts, max_attempts
FROM notification_queue
WHERE id = p_notification_id;

-- If we've reached max attempts, mark as failed and remove
IF current_attempts >= max_attempts THEN
DELETE FROM notification_queue
WHERE id = p_notification_id;
ELSE
    -- Reset to pending for retry
UPDATE notification_queue
SET
    status = 'pending',
    error_message = p_error_message
WHERE id = p_notification_id;
END IF;

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
deleted_count INTEGER;
BEGIN
  -- Delete expired notifications
DELETE FROM notification_queue
WHERE expires_at <= NOW();

GET DIAGNOSTICS deleted_count = ROW_COUNT;

RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get queue statistics
CREATE OR REPLACE FUNCTION get_queue_statistics()
RETURNS TABLE (
  total_pending INTEGER,
  total_processing INTEGER,
  total_expired INTEGER,
  oldest_pending TIMESTAMPTZ,
  newest_pending TIMESTAMPTZ
) AS $$
BEGIN
RETURN QUERY
SELECT
    COUNT(*) FILTER (WHERE status = 'pending')::INTEGER as total_pending,
        COUNT(*) FILTER (WHERE status = 'processing')::INTEGER as total_processing,
        COUNT(*) FILTER (WHERE expires_at <= NOW())::INTEGER as total_expired,
        MIN(created_at) FILTER (WHERE status = 'pending') as oldest_pending,
        MAX(created_at) FILTER (WHERE status = 'pending') as newest_pending
FROM notification_queue;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users and service role
GRANT EXECUTE ON FUNCTION queue_notification_for_device TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION queue_notification_for_user TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_pending_notifications TO service_role;
GRANT EXECUTE ON FUNCTION mark_notification_processing TO service_role;
GRANT EXECUTE ON FUNCTION mark_notification_sent TO service_role;
GRANT EXECUTE ON FUNCTION mark_notification_failed TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_expired_notifications TO service_role;
GRANT EXECUTE ON FUNCTION get_queue_statistics TO authenticated, service_role;



-- Notification Queue Automation
-- Sets up automatic processing using pg_cron and database triggers

-- Enable pg_cron extension for scheduled jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Function to call the Edge Function for processing notifications
CREATE OR REPLACE FUNCTION process_notification_queue_job()
RETURNS void AS $$
DECLARE
response_status integer;
  response_body text;
BEGIN
  -- Call the Edge Function using http extension
SELECT status, content INTO response_status, response_body
FROM http_post(
        'https://your-project-ref.supabase.co/functions/v1/process-notification-queue',
        '{}',
        'application/json',
        ARRAY[
            http_header('Authorization', 'Bearer ' || current_setting('app.settings.service_role_key', true))
            ]
     );

-- Log the result
IF response_status = 200 THEN
    RAISE NOTICE 'Notification queue processed successfully: %', response_body;
ELSE
    RAISE WARNING 'Notification queue processing failed with status %: %', response_status, response_body;
END IF;

EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error calling notification queue processor: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to call the Edge Function for cleanup
CREATE OR REPLACE FUNCTION cleanup_notification_queue_job()
RETURNS void AS $$
DECLARE
response_status integer;
  response_body text;
BEGIN
  -- Call the Edge Function using http extension
SELECT status, content INTO response_status, response_body
FROM http_post(
        'https://your-project-ref.supabase.co/functions/v1/cleanup-notification-queue',
        '{}',
        'application/json',
        ARRAY[
            http_header('Authorization', 'Bearer ' || current_setting('app.settings.service_role_key', true))
            ]
     );

-- Log the result
IF response_status = 200 THEN
    RAISE NOTICE 'Notification queue cleanup completed: %', response_body;
ELSE
    RAISE WARNING 'Notification queue cleanup failed with status %: %', response_status, response_body;
END IF;

EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error calling notification queue cleanup: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Alternative: Direct processing function (if you prefer database-only approach)
CREATE OR REPLACE FUNCTION process_notifications_direct(p_batch_size INTEGER DEFAULT 50)
RETURNS TABLE (
  processed INTEGER,
  sent INTEGER,
  failed INTEGER
) AS $$
DECLARE
notification_record RECORD;
  processed_count INTEGER := 0;
  sent_count INTEGER := 0;
  failed_count INTEGER := 0;
BEGIN
  -- Get pending notifications
FOR notification_record IN
SELECT
    nq.id,
    nq.user_id,
    nq.target_device_id,
    nt.push_token,
    nt.device_type,
    nq.title,
    nq.body,
    nq.data,
    nq.channel_type,
    nq.priority,
    nq.attempt_count,
    nq.max_attempts
FROM notification_queue nq
         JOIN notification_targets nt ON nq.target_device_id = nt.id
WHERE nq.status = 'pending'
  AND nq.scheduled_for <= NOW()
  AND nq.expires_at > NOW()
  AND nt.is_active = true
ORDER BY nq.priority DESC, nq.created_at ASC
    LIMIT p_batch_size
  LOOP
    processed_count := processed_count + 1;

-- Mark as processing
PERFORM mark_notification_processing(notification_record.id);

    -- Here you would normally call external API
    -- For now, we'll simulate success/failure
    IF random() > 0.1 THEN -- 90% success rate
      PERFORM mark_notification_sent(notification_record.id);
      sent_count := sent_count + 1;
ELSE
      PERFORM mark_notification_failed(notification_record.id, 'Simulated failure');
      failed_count := failed_count + 1;
END IF;
END LOOP;

RETURN QUERY SELECT processed_count, sent_count, failed_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function to automatically process new notifications
CREATE OR REPLACE FUNCTION trigger_notification_processing()
RETURNS TRIGGER AS $$
BEGIN
  -- Only trigger for new pending notifications
  IF NEW.status = 'pending' AND NEW.scheduled_for <= NOW() THEN
    -- Schedule immediate processing (you can adjust this logic)
    PERFORM pg_notify('process_notifications', NEW.id::text);
END IF;

RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic processing
DROP TRIGGER IF EXISTS auto_process_notifications ON notification_queue;
CREATE TRIGGER auto_process_notifications
    AFTER INSERT OR UPDATE ON notification_queue
                        FOR EACH ROW
                        EXECUTE FUNCTION trigger_notification_processing();

-- Schedule cron jobs (adjust timing as needed)
-- Process notifications every minute
SELECT cron.schedule(
               'process-notifications',
               '* * * * *', -- Every minute
               'SELECT process_notification_queue_job();'
       );

-- Cleanup expired notifications every 5 minutes
SELECT cron.schedule(
               'cleanup-notifications',
               '*/5 * * * *', -- Every 5 minutes
               'SELECT cleanup_notification_queue_job();'
       );

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION process_notification_queue_job TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_notification_queue_job TO service_role;
GRANT EXECUTE ON FUNCTION process_notifications_direct TO service_role;

-- Create a settings table for configuration
CREATE TABLE IF NOT EXISTS notification_queue_settings (
                                                           key TEXT PRIMARY KEY,
                                                           value JSONB NOT NULL,
                                                           description TEXT,
                                                           updated_at TIMESTAMPTZ DEFAULT NOW()
    );

-- Insert default settings
INSERT INTO notification_queue_settings (key, value, description) VALUES
                                                                      ('processing_enabled', 'true', 'Enable/disable automatic notification processing'),
                                                                      ('batch_size', '50', 'Number of notifications to process per batch'),
                                                                      ('processing_interval_seconds', '60', 'How often to process notifications (seconds)'),
                                                                      ('cleanup_interval_seconds', '300', 'How often to cleanup expired notifications (seconds)'),
                                                                      ('max_retry_attempts', '3', 'Maximum retry attempts for failed notifications'),
                                                                      ('stuck_processing_timeout_minutes', '10', 'Reset processing notifications older than this')
    ON CONFLICT (key) DO NOTHING;

-- Function to get setting value
CREATE OR REPLACE FUNCTION get_notification_setting(setting_key TEXT)
RETURNS JSONB AS $$
DECLARE
setting_value JSONB;
BEGIN
SELECT value INTO setting_value
FROM notification_queue_settings
WHERE key = setting_key;

RETURN COALESCE(setting_value, 'null'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update setting value
CREATE OR REPLACE FUNCTION update_notification_setting(setting_key TEXT, setting_value JSONB)
RETURNS BOOLEAN AS $$
BEGIN
UPDATE notification_queue_settings
SET value = setting_value, updated_at = NOW()
WHERE key = setting_key;

RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for settings
GRANT SELECT, UPDATE ON notification_queue_settings TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_notification_setting TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION update_notification_setting TO authenticated, service_role;
