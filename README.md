# OneSub

#
## Directories
```
OneSubMobile
├── app
│   ├── components
│   ├── config
│   ├── i18n
│   ├── models
│   ├── navigators
│   ├── screens
│   ├── services
│   ├── theme
│   ├── utils
│   └── app.tsx
├── assets
│   ├── icons
│   └── images
├── test
│   ├── __snapshots__
│   ├── mockFile.ts
│   └── setup.ts
├── README.md
├── android
│   ├── app
│   ├── build.gradle
│   ├── gradle
│   ├── gradle.properties
│   ├── gradlew
│   ├── gradlew.bat
│   ├── keystores
│   └── settings.gradle
├── ignite
│   └── templates
|       |── app-icon
│       ├── component
│       ├── model
│       ├── navigator
│       └── screen
├── index.js
├── ios
│   ├── IgniteProject
│   ├── IgniteProject-tvOS
│   ├── IgniteProject-tvOSTests
│   ├── IgniteProject.xcodeproj
│   └── IgniteProjectTests
├── .env
└── package.json

```

### ./app directory


The inside of the `app` directory looks similar to the following:

```
app
├── components
├── config
├── i18n
├── models
├── navigators
├── screens
├── services
├── theme
├── utils
└── app.tsx
```

**components**
This is where your reusable components live which help you build your screens.

**i18n**
This is where your translations will live if you are using `react-native-i18n`.

**models**
This is where your app's models will live. Each model has a directory which will contain the `mobx-state-tree` model file, test file, and any other supporting files like actions, types, etc.

**navigators**
This is where your `react-navigation` navigators will live.

**screens**
This is where your screen components will live. A screen is a React component which will take up the entire screen and be part of the navigation hierarchy. Each screen will have a directory containing the `.tsx` file, along with any assets or other helper files.

**services**
Any services that interface with the outside world will live here (think REST APIs, Push Notifications, etc.).

**theme**
Here lives the theme for your application, including spacing, colors, and typography.

**utils**
This is a great place to put miscellaneous helpers and utilities. Things like date helpers, formatters, etc. are often found here. However, it should only be used for things that are truly shared across your application. If a helper or utility is only used by a specific component or model, consider co-locating your helper with that component or model.

**app.tsx** This is the entry point to your app. This is where you will find the main App component which renders the rest of the application.

### ./assets directory

This directory is designed to organize and store various assets, making it easy for you to manage and use them in your application. The assets are further categorized into subdirectories, including `icons` and `images`:

```
assets
├── icons
└── images
```

**icons**
This is where your icon assets will live. These icons can be used for buttons, navigation elements, or any other UI components. The recommended format for icons is PNG, but other formats can be used as well.

Ignite comes with a built-in `Icon` component. You can find detailed usage instructions in the [docs](https://github.com/infinitered/ignite/blob/master/docs/Components-Icon.md).

**images**
This is where your images will live, such as background images, logos, or any other graphics. You can use various formats such as PNG, JPEG, or GIF for your images.

Another valuable built-in component within Ignite is the `AutoImage` component. You can find detailed usage instructions in the [docs](https://github.com/infinitered/ignite/blob/master/docs/Components-AutoImage.md).

How to use your `icon` or `image` assets:

```
import { Image } from 'react-native';

const MyComponent = () => {
  return (
    <Image source={require('../assets/images/my_image.png')} />
  );
};
```

### ./ignite directory

The `ignite` directory stores all things Ignite, including CLI and boilerplate items. Here you will find templates you can customize to help you get started with React Native.

### ./test directory

This directory will hold your Jest configs and mocks.

## Notifications

This app includes a comprehensive push notification system built with **Expo Notifications** and **Supabase** backend integration.

### Architecture
- **Frontend**: Expo Notifications with TypeScript service layer
- **Backend**: Supabase PostgreSQL with `notification_targets` table
- **State Management**: MobX State Tree integration
- **Channels**: Three Android notification channels (Questions, Recommendations, Other)
- **Features**: Local notifications, push notifications, custom actions, preference management

### Prerequisites

1. **Environment Variables**
   Add to your `.env` file:
   ```bash
   EXPO_PUBLIC_PROJECT_ID=d7f916e3-23fe-42eb-8bbb-44ba2dc5170d
   ```

### Usage Examples

**Send Local Notification with Custom Actions**
```typescript
import { useNotifications } from 'app/hooks/useNotifications'

const { sendLocalNotification } = useNotifications()

await sendLocalNotification({
  content: {
    title: 'Profile Question',
    body: 'Complete your profile questions',
    data: {
      channelType: 'questions',
      actions: [{
        type: 'navigate',
        payload: { screen: 'ProfileQuestionModal', params: { autoStart: true } }
      }]
    }
  },
  trigger: { type: 'immediate' }
})
```

**Manage Notification Preferences**
```typescript
const { notifications } = useStores()
const { updatePreferences } = useNotifications()

// Update preferences locally and sync to backend
await updatePreferences({
  questions: true,
  recommendations: false,
  other: true
})

// Save to Supabase
await notifications.savePreferences(userId)
```

### Android Notification Channels

- **Questions** (High Priority): Profile completion notifications with sound and vibration
- **Recommendations** (Default Priority): Product and service recommendations with sound only
- **Other** (Low Priority): General app notifications, silent with badge only

### Troubleshooting

**Permissions Issues**
- Ensure notifications are enabled in device settings
- Check `notifications.permissions.granted` status
- Use `requestPermissions()` to prompt user

**Device Requirements**
- Push notifications only work on physical devices
- Expo Go app may have limitations
- Test on development builds (EAS) for full functionality

**Token Registration**
- Automatic registration happens after authentication
- Check `notifications.pushToken` for current token
- Verify `notification_targets` table in Supabase


## Running Maestro end-to-end tests

Follow our [Maestro Setup](https://ignitecookbook.com/docs/recipes/MaestroSetup) recipe from the [Ignite Cookbook](https://ignitecookbook.com/)!
