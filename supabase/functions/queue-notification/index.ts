/**
 * Supabase Edge Function: Queue Notification
 * API endpoint for queuing notifications from your backend
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface QueueNotificationRequest {
  user_id: string
  target_device_id?: string // If not provided, sends to all user devices
  title: string
  body: string
  data?: Record<string, any>
  channel_type?: 'questions' | 'recommendations' | 'other'
  ttl_minutes?: number
  priority?: number
  scheduled_for?: string // ISO string
}

serve(async (req) => {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { status: 405, headers: { 'Content-Type': 'application/json' } }
    )
  }

  try {
    // Initialize Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Parse request body
    const requestBody: QueueNotificationRequest = await req.json()

    // Validate required fields
    if (!requestBody.user_id || !requestBody.title || !requestBody.body) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields', 
          required: ['user_id', 'title', 'body'] 
        }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Set defaults
    const {
      user_id,
      target_device_id,
      title,
      body,
      data = {},
      channel_type = 'other',
      ttl_minutes = 60,
      priority = 0,
      scheduled_for
    } = requestBody

    let result
    let notificationIds: string | string[]

    if (target_device_id) {
      // Queue for specific device
      const { data, error } = await supabase.rpc('queue_notification_for_device', {
        p_user_id: user_id,
        p_target_device_id: target_device_id,
        p_title: title,
        p_body: body,
        p_data: data,
        p_channel_type: channel_type,
        p_ttl_minutes: ttl_minutes,
        p_priority: priority,
        p_scheduled_for: scheduled_for || new Date().toISOString()
      })

      if (error) throw error
      
      notificationIds = data
      result = {
        success: true,
        message: 'Notification queued for device',
        notification_id: data,
        target_type: 'device'
      }
    } else {
      // Queue for all user devices
      const { data, error } = await supabase.rpc('queue_notification_for_user', {
        p_user_id: user_id,
        p_title: title,
        p_body: body,
        p_data: data,
        p_channel_type: channel_type,
        p_ttl_minutes: ttl_minutes,
        p_priority: priority,
        p_scheduled_for: scheduled_for || new Date().toISOString()
      })

      if (error) throw error
      
      notificationIds = data || []
      result = {
        success: true,
        message: 'Notification queued for user',
        notification_ids: data,
        device_count: data?.length || 0,
        target_type: 'user'
      }
    }

    console.log('Notification queued successfully:', {
      user_id,
      target_device_id,
      title,
      channel_type,
      notification_ids: notificationIds
    })

    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error queuing notification:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to queue notification', 
        details: error.message 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})

/* 
Usage Examples:

1. Queue notification for specific device:
POST /functions/v1/queue-notification
{
  "user_id": "user-123",
  "target_device_id": "device-456",
  "title": "Profile Question",
  "body": "Please complete your profile",
  "channel_type": "questions",
  "ttl_minutes": 60,
  "priority": 1,
  "data": {
    "question_id": "q123",
    "action": "complete_profile"
  }
}

2. Queue notification for all user devices:
POST /functions/v1/queue-notification
{
  "user_id": "user-123",
  "title": "New Recommendation",
  "body": "Check out this product recommendation",
  "channel_type": "recommendations",
  "ttl_minutes": 120,
  "data": {
    "product_id": "prod-789",
    "category": "electronics"
  }
}

3. Schedule notification for later:
POST /functions/v1/queue-notification
{
  "user_id": "user-123",
  "title": "Reminder",
  "body": "Don't forget to check your recommendations",
  "scheduled_for": "2025-01-16T15:30:00Z",
  "ttl_minutes": 30
}
*/
