// Simple test to check subscription data structure
// Run this in your browser console or as a Node.js script

const testQuery = `
  SELECT 
    us.id,
    us.user_id,
    us.product_id,
    us.started_on,
    us.ended_on,
    p.name as product_name,
    p.description as product_description
  FROM user_subscriptions us
  JOIN products p ON us.product_id = p.id
  LIMIT 5;
`;

console.log('Test query to run in Supabase SQL editor:');
console.log(testQuery);

// Also test the merchant_products relationship
const merchantProductsQuery = `
  SELECT 
    mp.merchant_id,
    mp.product_id,
    m.name as merchant_name,
    p.name as product_name
  FROM merchant_products mp
  JOIN merchants m ON mp.merchant_id = m.id
  JOIN products p ON mp.product_id = p.id
  LIMIT 5;
`;

console.log('\nMerchant-Products relationship query:');
console.log(merchantProductsQuery);

// Test the full join we need
const fullQuery = `
  SELECT 
    us.id,
    us.user_id,
    us.product_id,
    us.started_on,
    us.ended_on,
    p.name as product_name,
    m.name as merchant_name,
    mg.name as merchant_group_name
  FROM user_subscriptions us
  JOIN products p ON us.product_id = p.id
  JOIN merchant_products mp ON p.id = mp.product_id
  JOIN merchants m ON mp.merchant_id = m.id
  LEFT JOIN merchant_groups mg ON m.merchant_group_id = mg.id
  LIMIT 5;
`;

console.log('\nFull join query to test:');
console.log(fullQuery);
