/**
 * Example: Using the Notification Queue System from your backend
 * This shows how to queue notifications from your server-side code
 */

// Example 1: Queue notification when user completes an action
export async function notifyUserProfileComplete(userId: string) {
  const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/queue-notification`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: userId,
      title: 'Profile Complete! 🎉',
      body: 'Your profile is now complete. Check out personalized recommendations.',
      channel_type: 'other',
      ttl_minutes: 120,
      priority: 1,
      data: {
        action: 'view_recommendations',
        deep_link: '/recommendations'
      }
    })
  })

  const result = await response.json()
  console.log('Profile completion notification queued:', result)
  return result
}

// Example 2: Queue urgent security notification for specific device
export async function notifySecurity<PERSON>lert(userId: string, deviceId: string, alertType: string) {
  const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/queue-notification`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: userId,
      target_device_id: deviceId, // Send to specific device only
      title: 'Security Alert 🔒',
      body: `Unusual ${alertType} detected on your account`,
      channel_type: 'other',
      ttl_minutes: 15, // Short TTL for urgent notifications
      priority: 10, // High priority
      data: {
        alert_type: alertType,
        action: 'review_security',
        deep_link: '/security/alerts'
      }
    })
  })

  const result = await response.json()
  console.log('Security alert notification queued:', result)
  return result
}

// Example 3: Queue weekly recommendations for all user devices
export async function notifyWeeklyRecommendations(userId: string, recommendations: any[]) {
  const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/queue-notification`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: userId,
      // No target_device_id = send to all user devices
      title: 'New Recommendations Available',
      body: `We found ${recommendations.length} new recommendations for you!`,
      channel_type: 'recommendations',
      ttl_minutes: 1440, // 24 hours
      priority: 0,
      data: {
        recommendation_count: recommendations.length,
        week: new Date().toISOString().slice(0, 10),
        action: 'view_recommendations',
        deep_link: '/recommendations'
      }
    })
  })

  const result = await response.json()
  console.log('Weekly recommendations notification queued:', result)
  return result
}

// Example 4: Schedule reminder notification for later
export async function scheduleProfileReminder(userId: string, reminderTime: Date) {
  const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/queue-notification`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: userId,
      title: 'Complete Your Profile',
      body: 'Finish setting up your profile to get better recommendations',
      channel_type: 'questions',
      ttl_minutes: 60,
      priority: 0,
      scheduled_for: reminderTime.toISOString(), // Schedule for specific time
      data: {
        reminder_type: 'profile_completion',
        action: 'complete_profile',
        deep_link: '/profile/questions'
      }
    })
  })

  const result = await response.json()
  console.log('Profile reminder scheduled:', result)
  return result
}

// Example 5: Batch queue multiple notifications
export async function queueMultipleNotifications(notifications: Array<{
  userId: string,
  title: string,
  body: string,
  channelType?: string,
  ttlMinutes?: number,
  priority?: number,
  data?: any
}>) {
  const results = []
  
  for (const notification of notifications) {
    try {
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/queue-notification`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user_id: notification.userId,
          title: notification.title,
          body: notification.body,
          channel_type: notification.channelType || 'other',
          ttl_minutes: notification.ttlMinutes || 60,
          priority: notification.priority || 0,
          data: notification.data || {}
        })
      })

      const result = await response.json()
      results.push({ success: true, ...result })
    } catch (error) {
      results.push({ 
        success: false, 
        error: error.message,
        userId: notification.userId 
      })
    }
  }

  console.log(`Queued ${results.filter(r => r.success).length}/${notifications.length} notifications`)
  return results
}

// Example 6: Integration with your existing business logic
export class NotificationManager {
  private supabaseUrl: string
  private serviceRoleKey: string

  constructor() {
    this.supabaseUrl = process.env.SUPABASE_URL!
    this.serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
  }

  async queueNotification(params: {
    userId: string
    deviceId?: string
    title: string
    body: string
    channelType?: 'questions' | 'recommendations' | 'other'
    ttlMinutes?: number
    priority?: number
    scheduledFor?: Date
    data?: Record<string, any>
  }) {
    try {
      const response = await fetch(`${this.supabaseUrl}/functions/v1/queue-notification`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.serviceRoleKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user_id: params.userId,
          target_device_id: params.deviceId,
          title: params.title,
          body: params.body,
          channel_type: params.channelType || 'other',
          ttl_minutes: params.ttlMinutes || 60,
          priority: params.priority || 0,
          scheduled_for: params.scheduledFor?.toISOString(),
          data: params.data || {}
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to queue notification:', error)
      throw error
    }
  }

  // Business logic methods
  async onUserSignup(userId: string) {
    return this.queueNotification({
      userId,
      title: 'Welcome to OneSub! 👋',
      body: 'Complete your profile to get personalized recommendations',
      channelType: 'questions',
      priority: 1,
      data: { onboarding_step: 'profile_setup' }
    })
  }

  async onQuestionAnswered(userId: string, questionId: string) {
    return this.queueNotification({
      userId,
      title: 'Great Progress! 📈',
      body: 'Your profile is getting better. Keep answering questions!',
      channelType: 'questions',
      ttlMinutes: 30,
      data: { question_id: questionId, action: 'continue_questions' }
    })
  }

  async onNewRecommendation(userId: string, productName: string) {
    return this.queueNotification({
      userId,
      title: 'New Recommendation',
      body: `Check out ${productName} - it matches your preferences!`,
      channelType: 'recommendations',
      ttlMinutes: 240, // 4 hours
      data: { product_name: productName, action: 'view_recommendation' }
    })
  }
}

// Export singleton instance
export const notificationManager = new NotificationManager()

// Usage in your application:
// await notificationManager.onUserSignup('user-123')
// await notificationManager.onNewRecommendation('user-123', 'Netflix Premium')
