import React from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle, ImageStyle, Pressable } from "react-native"
import { Text, AutoImage } from "app/components"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import { colors, spacing } from "app/theme"
import { getCurrencyIcon } from "app/utils/currencyUtils"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"

export interface SubscriptionListItemProps {
  /**
   * The subscription data to display
   */
  subscription: UserSubscription
  /**
   * Optional callback when the item is pressed
   */
  onPress?: (subscription: UserSubscription) => void
  /**
   * Optional style override
   */
  style?: ViewStyle
}

/**
 * A component to display a subscription item in a list
 */
export const SubscriptionListItem = observer(function SubscriptionListItem(props: SubscriptionListItemProps) {
  const { subscription, onPress, style } = props

  const handlePress = () => {
    onPress?.(subscription)
  }

  const Wrapper = onPress ? Pressable : View

  return (
    <Wrapper style={[$container, style]} onPress={handlePress}>
      <View style={$logoContainer}>
        {subscription.merchantGroupLogo ? (
          <AutoImage
            source={{ uri: subscription.merchantGroupLogo }}
            style={$logo}
            maxWidth={40}
            maxHeight={40}
          />
        ) : (
          <View style={$placeholderLogo}>
            <MaterialCommunityIcons
              name="store"
              size={24}
              color={colors.palette.neutral400}
            />
          </View>
        )}
      </View>

      <View style={$contentContainer}>
        <Text style={$productName} numberOfLines={1}>
          {subscription.productName}
        </Text>
        {subscription.billingPeriod && (
          <Text style={$billingPeriod} numberOfLines={1}>
            {subscription.billingPeriod}
          </Text>
        )}
      </View>

      <View style={$costContainer}>
        <View style={$costRow}>
          <MaterialCommunityIcons
            name={getCurrencyIcon(subscription.currency)}
            size={16}
            color={colors.palette.primary600}
            style={$currencyIcon}
          />
          <Text style={$cost}>
            {subscription.formattedCost}
          </Text>
        </View>
      </View>
    </Wrapper>
  )
})

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.md,
  backgroundColor: colors.background,
  borderBottomWidth: 1,
  borderBottomColor: colors.border,
}

const $logoContainer: ViewStyle = {
  marginRight: spacing.md,
}

const $logo: ImageStyle = {
  width: 40,
  height: 40,
  borderRadius: 8,
}

const $placeholderLogo: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
}

const $contentContainer: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm,
}

const $productName: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.text,
  marginBottom: spacing.xxs,
}

const $billingPeriod: TextStyle = {
  fontSize: 14,
  color: colors.textDim,
}

const $costContainer: ViewStyle = {
  alignItems: "flex-end",
}

const $costRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $currencyIcon: ViewStyle = {
  marginRight: spacing.xxs,
}

const $cost: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.palette.primary600,
}
