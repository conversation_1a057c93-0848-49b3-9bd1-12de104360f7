import React from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle, ImageStyle, Pressable } from "react-native"
import { Text, AutoImage, Card } from "app/components"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import { colors, spacing } from "app/theme"
import { getCurrencyIcon } from "app/utils/currencyUtils"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"

export interface SubscriptionCardProps {
  /**
   * The subscription data to display
   */
  subscription: UserSubscription
  /**
   * Optional callback when the card is pressed
   */
  onPress?: (subscription: UserSubscription) => void
  /**
   * Optional style override
   */
  style?: ViewStyle
}

/**
 * A card component to display a subscription
 */
export const SubscriptionCard = observer(function SubscriptionCard(props: SubscriptionCardProps) {
  const { subscription, onPress, style } = props

  const handlePress = () => {
    onPress?.(subscription)
  }

  return (
    <Card
      style={[$card, style]}
      onPress={handlePress}
      LeftComponent={
        <View style={$logoContainer}>
          {subscription.merchantGroupLogo ? (
            <AutoImage
              source={{ uri: subscription.merchantGroupLogo }}
              style={$logo}
              maxWidth={50}
              maxHeight={50}
            />
          ) : (
            <View style={$placeholderLogo}>
              <MaterialCommunityIcons
                name="store"
                size={28}
                color={colors.palette.neutral400}
              />
            </View>
          )}
        </View>
      }
      RightComponent={
        <View style={$costContainer}>
          <View style={$costRow}>
            <MaterialCommunityIcons
              name={getCurrencyIcon(subscription.currency)}
              size={18}
              color={colors.palette.primary600}
              style={$currencyIcon}
            />
            <Text style={$cost}>
              {subscription.numericCost.toFixed(2)}
            </Text>
          </View>
          {subscription.billingPeriod && (
            <Text style={$billingPeriod}>
              per {subscription.billingPeriod.toLowerCase()}
            </Text>
          )}
        </View>
      }
    >
      <View style={$contentContainer}>
        <Text style={$productName} numberOfLines={2}>
          {subscription.productName}
        </Text>
        {subscription.product?.description && (
          <Text style={$description} numberOfLines={2}>
            {subscription.product.description}
          </Text>
        )}
      </View>
    </Card>
  )
})

const $card: ViewStyle = {
  marginHorizontal: spacing.md,
  marginVertical: spacing.xs,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.border,
  borderRadius: 12,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 2,
}

const $logoContainer: ViewStyle = {
  marginRight: spacing.md,
}

const $logo: ImageStyle = {
  width: 50,
  height: 50,
  borderRadius: 8,
}

const $placeholderLogo: ViewStyle = {
  width: 50,
  height: 50,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
}

const $contentContainer: ViewStyle = {
  flex: 1,
}

const $productName: TextStyle = {
  fontSize: 18,
  fontWeight: "600",
  color: colors.text,
  marginBottom: spacing.xs,
}

const $description: TextStyle = {
  fontSize: 14,
  color: colors.textDim,
  lineHeight: 20,
}

const $costContainer: ViewStyle = {
  alignItems: "flex-end",
  justifyContent: "center",
}

const $costRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: spacing.xxs,
}

const $currencyIcon: ViewStyle = {
  marginRight: spacing.xxs,
}

const $cost: TextStyle = {
  fontSize: 20,
  fontWeight: "700",
  color: colors.palette.primary600,
}

const $billingPeriod: TextStyle = {
  fontSize: 12,
  color: colors.textDim,
  textAlign: "right",
}
