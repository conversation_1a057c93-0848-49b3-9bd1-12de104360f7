import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

export interface SubscriptionGroupHeaderProps {
  category: string
  count: number
}

export function SubscriptionGroupHeader({ category, count }: SubscriptionGroupHeaderProps) {
  return (
    <View style={$container}>
      <Text style={$categoryText}>
        {category}
      </Text>
      <Text style={$countText}>
        {count} {count === 1 ? 'subscription' : 'subscriptions'}
      </Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  backgroundColor: colors.palette.neutral200,
  borderBottomWidth: 1,
  borderBottomColor: colors.separator,
}

const $categoryText: TextStyle = {
  fontSize: 18,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  textTransform: 'capitalize',
}

const $countText: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}
