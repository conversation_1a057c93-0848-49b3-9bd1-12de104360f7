import React, { useState } from "react"
import { View, Pressable, ViewStyle, TextStyle, Platform } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import DateTimePicker from '@react-native-community/datetimepicker'
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faCalendarAlt } from "@fortawesome/free-solid-svg-icons"

export interface DatePickerFieldProps {
  label: string
  value: Date
  onChange: (date: Date) => void
  icon?: boolean
  style?: ViewStyle
}

export function DatePickerField({
  label,
  value,
  onChange,
  icon = true,
  style
}: DatePickerFieldProps) {
  const [showPicker, setShowPicker] = useState(false)

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowPicker(false)
    if (selectedDate) {
      onChange(selectedDate)
    }
  }

  const handlePress = () => {
    setShowPicker(true)
  }

  return (
    <View style={[style]}>
      <View style={$labelContainer}>
        {icon && (
          <FontAwesomeIcon
            icon={faCalendarAlt}
            size={16}
            color={colors.palette.primary500}
          />
        )}
        <Text style={$label}>{label}</Text>
      </View>

      <Pressable
        style={$input}
        onPress={handlePress}
      >
        <Text style={$dateText}>
          {value.toLocaleDateString()}
        </Text>
      </Pressable>

      {showPicker && (
        <DateTimePicker
          value={value}
          mode="date"
          display={Platform.OS === 'ios' ? 'inline' : 'default'}
          onChange={handleDateChange}
        />
      )}
    </View>
  )
}

const $labelContainer: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
  marginBottom: spacing.xs,
}

const $label: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.medium,
  marginLeft: spacing.xs,
  color: colors.text,
}

const $input: ViewStyle = {
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  borderRadius: 8,
  padding: spacing.sm,
  backgroundColor: colors.palette.neutral50,
  minHeight: 48,
  justifyContent: 'center',
}

const $dateText: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.text,
}
