import React, { useCallback } from "react"
import { observer } from "mobx-react-lite"
import { SectionList, RefreshControl, View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { SubscriptionItem } from "./SubscriptionItem"
import { SubscriptionGroupHeader } from "./SubscriptionGroupHeader"
import { colors, spacing, typography } from "app/theme"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"

export interface SubscriptionGroup {
  category: string
  data: UserSubscription[]
}

export interface SubscriptionsListProps {
  sections: SubscriptionGroup[]
  loading: boolean
  onRefresh: () => void
  onSubscriptionPress?: (subscription: UserSubscription) => void
  ListEmptyComponent?: React.ComponentType<any> | React.ReactElement | null
}

export const SubscriptionsList = observer(function SubscriptionsList({
  sections,
  loading,
  onRefresh,
  onSubscriptionPress,
  ListEmptyComponent
}: SubscriptionsListProps) {
  const renderSectionHeader = useCallback(({ section }: { section: SubscriptionGroup }) => (
    <SubscriptionGroupHeader 
      category={section.category} 
      count={section.data.length} 
    />
  ), [])

  const renderItem = useCallback(({ item }: { item: UserSubscription }) => (
    <SubscriptionItem 
      subscription={item} 
      onPress={onSubscriptionPress}
    />
  ), [onSubscriptionPress])

  const keyExtractor = useCallback((item: UserSubscription) => item.id, [])

  const getItemLayout = useCallback((data: any, index: number) => ({
    length: 88, // Approximate height of SubscriptionItem
    offset: 88 * index,
    index,
  }), [])

  const ItemSeparatorComponent = useCallback(() => <View style={$separator} />, [])

  const SectionSeparatorComponent = useCallback(() => <View style={$sectionSeparator} />, [])

  if (!loading && sections.length === 0) {
    return ListEmptyComponent ? (
      typeof ListEmptyComponent === 'function' ? <ListEmptyComponent /> : ListEmptyComponent
    ) : (
      <DefaultEmptyComponent />
    )
  }

  return (
    <SectionList
      sections={sections}
      renderItem={renderItem}
      renderSectionHeader={renderSectionHeader}
      keyExtractor={keyExtractor}
      getItemLayout={getItemLayout}
      ItemSeparatorComponent={ItemSeparatorComponent}
      SectionSeparatorComponent={SectionSeparatorComponent}
      refreshControl={
        <RefreshControl
          refreshing={loading}
          onRefresh={onRefresh}
          tintColor={colors.tint}
          colors={[colors.tint]}
        />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={$contentContainer}
      stickySectionHeadersEnabled={true}
    />
  )
})

function DefaultEmptyComponent() {
  return (
    <View style={$emptyContainer}>
      <Text style={$emptyTitle}>No Subscriptions Yet</Text>
      <Text style={$emptyMessage}>
        Start tracking your subscriptions by tapping the + button below
      </Text>
    </View>
  )
}

const $contentContainer: ViewStyle = {
  paddingBottom: 100, // Space for floating add button and tab bar
}

const $separator: ViewStyle = {
  height: spacing.xxxs,
}

const $sectionSeparator: ViewStyle = {
  height: spacing.sm,
}

const $emptyContainer: ViewStyle = {
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.xxxl,
}

const $emptyTitle: TextStyle = {
  fontSize: 24,
  fontFamily: typography.primary.bold,
  color: colors.text,
  textAlign: 'center',
  marginBottom: spacing.sm,
}

const $emptyMessage: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  textAlign: 'center',
  lineHeight: 24,
}
