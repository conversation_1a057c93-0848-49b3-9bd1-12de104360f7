import React from "react"
import { Pressable, View, ActivityIndicator, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

export interface LoadingButtonProps {
  title: string
  onPress: () => void
  loading?: boolean
  disabled?: boolean
  loadingText?: string
  style?: ViewStyle
  textStyle?: TextStyle
}

export function LoadingButton({
  title,
  onPress,
  loading = false,
  disabled = false,
  loadingText = "Loading...",
  style,
  textStyle
}: LoadingButtonProps) {
  const isDisabled = disabled || loading

  return (
    <Pressable
      style={[
        $button,
        isDisabled && $buttonDisabled,
        style
      ]}
      onPress={onPress}
      disabled={isDisabled}
    >
      {loading ? (
        <View style={$loadingContainer}>
          <ActivityIndicator size="small" color="white" />
          <Text style={[$loadingText, textStyle]}>{loadingText}</Text>
        </View>
      ) : (
        <Text style={[$buttonText, isDisabled && $buttonTextDisabled, textStyle]}>
          {title}
        </Text>
      )}
    </Pressable>
  )
}

const $button: ViewStyle = {
  backgroundColor: colors.tint,
  padding: spacing.md,
  borderRadius: 12,
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: 56,
}

const $buttonDisabled: ViewStyle = {
  backgroundColor: colors.palette.neutral400,
}

const $buttonText: TextStyle = {
  color: colors.palette.neutral100,
  fontFamily: typography.primary.bold,
  fontSize: 16,
}

const $buttonTextDisabled: TextStyle = {
  color: colors.palette.neutral600,
}

const $loadingContainer: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
}

const $loadingText: TextStyle = {
  color: colors.palette.neutral100,
  fontFamily: typography.primary.medium,
  fontSize: 16,
  marginLeft: spacing.xs,
}
