/**
 * TypeScript interfaces and types for the notification system
 * Provides type safety for Expo Notifications integration with Supabase
 */

import { ExpoPushToken } from 'expo-notifications'

// =====================================================
// Database Types (matching Supabase schema)
// =====================================================

/**
 * Device type enumeration matching database constraint
 */
export type DeviceType = 'ios' | 'android' | 'web'

/**
 * Notification channel types for Android
 */
export type NotificationChannelType = 'questions' | 'recommendations' | 'other'

/**
 * Device information stored in JSONB field
 */
export interface DeviceInfo {
  device_name?: string
  os_version?: string
  app_version?: string
  device_model?: string
  [key: string]: any // Allow additional properties
}

/**
 * User notification preferences stored in JSONB field
 */
export interface NotificationPreferences {
  questions: boolean
  recommendations: boolean
  other: boolean
}

/**
 * Database record for notification_targets table
 */
export interface NotificationTarget {
  id: string
  user_id: string
  push_token: string
  device_type: DeviceType
  device_info: DeviceInfo
  notification_preferences: NotificationPreferences
  is_active: boolean
  created_at: string
  updated_at: string
  last_notification_at: string | null
}

/**
 * Input type for creating a new notification target
 */
export interface CreateNotificationTargetInput {
  user_id: string
  push_token: string
  device_type: DeviceType
  device_info?: DeviceInfo
  notification_preferences?: Partial<NotificationPreferences>
}

/**
 * Input type for updating an existing notification target
 */
export interface UpdateNotificationTargetInput {
  device_info?: DeviceInfo
  notification_preferences?: Partial<NotificationPreferences>
  is_active?: boolean
}

// =====================================================
// Expo Notifications Types
// =====================================================

/**
 * Local notification content structure
 */
export interface LocalNotificationContent {
  title: string
  body: string
  data?: Record<string, any>
  sound?: boolean | string
  badge?: number
  categoryIdentifier?: string
}

/**
 * Local notification trigger options
 */
export interface LocalNotificationTrigger {
  type: 'immediate' | 'date' | 'interval'
  date?: Date
  seconds?: number
  repeats?: boolean
}

/**
 * Complete local notification request
 */
export interface LocalNotificationRequest {
  identifier?: string
  content: LocalNotificationContent
  trigger: LocalNotificationTrigger
}

/**
 * Push notification payload structure
 */
export interface PushNotificationPayload {
  to: string | string[]
  title: string
  body: string
  data?: Record<string, any>
  sound?: 'default' | null
  badge?: number
  channelId?: string
  priority?: 'default' | 'normal' | 'high' | 'max'
  ttl?: number
}

/**
 * Push notification response from Expo
 */
export interface PushNotificationResponse {
  status: 'ok' | 'error'
  id?: string
  message?: string
  details?: any
}

// =====================================================
// Custom Action Types
// =====================================================

/**
 * Custom notification action types
 */
export type NotificationActionType = 
  | 'navigate'
  | 'api_call'
  | 'update_state'
  | 'show_modal'
  | 'refresh_data'
  | 'custom'

/**
 * Navigation action payload
 */
export interface NavigationActionPayload {
  screen: string
  params?: Record<string, any>
}

/**
 * API call action payload
 */
export interface ApiCallActionPayload {
  endpoint: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: any
  headers?: Record<string, string>
}

/**
 * State update action payload
 */
export interface StateUpdateActionPayload {
  store: string
  action: string
  payload?: any
}

/**
 * Custom notification action definition
 */
export interface NotificationAction {
  type: NotificationActionType
  payload: NavigationActionPayload | ApiCallActionPayload | StateUpdateActionPayload | any
}

/**
 * Notification with custom actions
 */
export interface NotificationWithActions {
  id: string
  title: string
  body: string
  data?: Record<string, any>
  actions?: NotificationAction[]
  channelType?: NotificationChannelType
  timestamp: Date
}

// =====================================================
// Permission and Status Types
// =====================================================

/**
 * Notification permission status
 */
export type NotificationPermissionStatus = 'granted' | 'denied' | 'undetermined'

/**
 * Notification settings status
 */
export interface NotificationSettings {
  granted: boolean
  ios?: {
    status: NotificationPermissionStatus
    allowsAlert: boolean
    allowsBadge: boolean
    allowsSound: boolean
    allowsCriticalAlerts: boolean
    allowsAnnouncements: boolean
    allowsDisplayInNotificationCenter: boolean
    allowsDisplayInCarPlay: boolean
    allowsDisplayOnLockScreen: boolean
  }
  android?: {
    status: NotificationPermissionStatus
    importance: number
  }
}

// =====================================================
// Service Response Types
// =====================================================

/**
 * Generic service response wrapper
 */
export interface ServiceResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  code?: string
}

/**
 * Notification service initialization result
 */
export interface NotificationServiceInitResult {
  success: boolean
  pushToken?: string
  permissions?: NotificationSettings
  error?: string
}

// =====================================================
// Android Channel Configuration
// =====================================================

/**
 * Android notification channel configuration
 */
export interface AndroidNotificationChannel {
  id: string
  name: string
  description?: string
  importance: number
  sound?: boolean
  vibrate?: boolean
  showBadge?: boolean
  lights?: boolean
  lightColor?: string
}

/**
 * Predefined Android channels for the app
 */
export const ANDROID_CHANNELS: Record<NotificationChannelType, AndroidNotificationChannel> = {
  questions: {
    id: 'questions',
    name: 'Profile Questions',
    description: 'Notifications about profile completion questions',
    importance: 4, // HIGH
    sound: true,
    vibrate: true,
    showBadge: true,
    lights: true,
    lightColor: '#FF6B35'
  },
  recommendations: {
    id: 'recommendations',
    name: 'Recommendations',
    description: 'Product and service recommendations',
    importance: 3, // DEFAULT
    sound: true,
    vibrate: false,
    showBadge: true,
    lights: false
  },
  other: {
    id: 'other',
    name: 'General Notifications',
    description: 'General app notifications and updates',
    importance: 2, // LOW
    sound: false,
    vibrate: false,
    showBadge: true,
    lights: false
  }
}

// =====================================================
// Error Types
// =====================================================

/**
 * Notification-specific error types
 */
export class NotificationError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: any
  ) {
    super(message)
    this.name = 'NotificationError'
  }
}

/**
 * Common notification error codes
 */
export const NOTIFICATION_ERROR_CODES = {
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  TOKEN_REGISTRATION_FAILED: 'TOKEN_REGISTRATION_FAILED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SUPABASE_ERROR: 'SUPABASE_ERROR',
  CHANNEL_CREATION_FAILED: 'CHANNEL_CREATION_FAILED',
  NOTIFICATION_SEND_FAILED: 'NOTIFICATION_SEND_FAILED'
} as const

export type NotificationErrorCode = typeof NOTIFICATION_ERROR_CODES[keyof typeof NOTIFICATION_ERROR_CODES]
