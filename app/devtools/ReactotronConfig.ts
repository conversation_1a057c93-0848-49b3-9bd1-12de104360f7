/**
 * This file does the setup for integration with Reactotron, which is a
 * free desktop app for inspecting and debugging your React Native app.
 * @see https://github.com/infinitered/reactotron
 */
import { Platform, NativeModules } from "react-native"

import AsyncStorage from "@react-native-async-storage/async-storage"
import { ArgType } from "reactotron-core-client"
import { mst } from "reactotron-mst"

import { clear } from "app/utils/storage"
import { goBack, resetRoot, navigate } from "app/navigators/navigationUtilities"

import { Reactotron } from "./ReactotronClient"
import {
  createPendingQuestionForUser,
  TestMerchantGroups,
  TestMerchants,
  TestProducts,
} from "app/models/Merchant/Examples"
import { getStore, RootStore } from "app/models"

const reactotron = Reactotron.configure({
  name: require("../../package.json").name,
  onConnect: () => {
    /** since this file gets hot reloaded, let's clear the past logs every time we connect */
    Reactotron.clear()
  },
}).use(
  mst({
    /* ignore some chatty `mobx-state-tree` actions */
    filter: (event) => /postProcessSnapshot|@APPLY_SNAPSHOT/.test(event.name) === false,
  }),
)

if (Platform.OS !== "web") {
  reactotron.setAsyncStorageHandler?.(AsyncStorage)
  reactotron.useReactNative({
    networking: {
      ignoreUrls: /symbolicate/,
    },
  })
}

/**
 * Reactotron allows you to define custom commands that you can run
 * from Reactotron itself, and they will run in your app.
 *
 * Define them in the section below with `onCustomCommand`. Use your
 * creativity -- this is great for development to quickly and easily
 * get your app into the state you want.
 *
 * NOTE: If you edit this file while running the app, you will need to do a full refresh
 * or else your custom commands won't be registered correctly.
 */
reactotron.onCustomCommand({
  title: "Show Dev Menu",
  description: "Opens the React Native dev menu",
  command: "showDevMenu",
  handler: () => {
    Reactotron.log("Showing React Native dev menu")
    NativeModules.DevMenu.show()
  },
})

reactotron.onCustomCommand({
  title: "Complete Onboarding",
  description: "By Pass Onboarding",
  command: "byPassOnboarding",
  handler: () => {
    Reactotron.log("bypassing Onboarding")
    const rootStore = getStore();
    rootStore.userState.completeOnboarding()
  },
})

reactotron.onCustomCommand({
  title: "Reset Root Store",
  description: "Resets the MST store",
  command: "resetStore",
  handler: () => {
    Reactotron.log("resetting store")
    clear()
  },
})
reactotron.onCustomCommand<[{ name: "node"; type: ArgType.String }]>({
  command: "clearNode",
  handler: (args) => {
    const { node } = args ?? {}
    if (node) {
      Reactotron.log(`Clearing: $node}`)
      const rootStore: RootStore = getStore()
      rootStore.clearNode(node)
    } else {
      Reactotron.log(`Could not clear rootStore at ${node}.`)
    }
  },
  title: "Clear root store node",
  description: "Clears a particular node in the rootStore.",
  args: [{ name: "node", type: ArgType.String }],
})
/**
 * Command: Add Pending Question for Current User
 */
reactotron.onCustomCommand({
  title: "Add Pending Question",
  description: "Adds a pending question for the currently logged-in user",
  command: "addPendingQuestion",
  handler: async () => {
    const rootStore: RootStore = getStore()
    try {
      // Get the current user's ID
      const userId = rootStore.auth.fetchAndCacheUserId()

      if (!userId) {
        console.error("Error: No user is currently logged in.")
        return
      }

      // Create a mock question for the user
      const pendingQuestion = createPendingQuestionForUser(userId as string)

      // Add the question to the PendingQuestionsStore
      rootStore.pendingQuestions.addPendingQuestion(
        pendingQuestion,
        "user",
        userId as string,
      )
      Reactotron.log(`Pending question added for user ID: ${userId}`, rootStore.pendingQuestions)
    } catch (error: any) {
      Reactotron.log(`Error adding pending question: ${error.message}`)
    }
  },
})


reactotron.onCustomCommand({
  title: "Add Test Products",
  description: "Adds test products to the store",
  command: "addTestProducts",
  handler: async () => {
    const rootStore = getStore();
    try {
      TestMerchantGroups.forEach(group => {
        rootStore.addMerchantGroup(group)
      })
      TestMerchants.forEach(merchant => {
        rootStore.addMerchant(merchant)
      })
      TestProducts.forEach(product => {
        rootStore.addProduct(product)
      })
      Reactotron.log("Test products added successfully", rootStore.products.length)
    } catch (error: any) {
      Reactotron.log(`Error adding test products: ${error.message}`)
    }
  },
})



reactotron.onCustomCommand({
  title: "Reset Navigation State",
  description: "Resets the navigation state",
  command: "resetNavigation",
  handler: () => {
    Reactotron.log("resetting navigation state")
    resetRoot({ index: 0, routes: [] })
  },
})

reactotron.onCustomCommand<[{ name: "route"; type: ArgType.String }]>({
  command: "navigateTo",
  handler: (args) => {
    const { route } = args ?? {}
    if (route) {
      Reactotron.log(`Navigating to: ${route}`)
      navigate(route as any) // this should be tied to the navigator, but since this is for debugging, we can navigate to illegal routes
    } else {
      Reactotron.log("Could not navigate. No route provided.")
    }
  },
  title: "Navigate To Screen",
  description: "Navigates to a screen by name.",
  args: [{ name: "route", type: ArgType.String }],
})

reactotron.onCustomCommand({
  title: "Go Back",
  description: "Goes back",
  command: "goBack",
  handler: () => {
    Reactotron.log("Going back")
    goBack()
  },
})

/**
 * We're going to add `console.tron` to the Reactotron object.
 * Now, anywhere in our app in development, we can use Reactotron like so:
 *
 * ```
 * if (__DEV__) {
 *  console.tron.display({
 *    name: 'JOKE',
 *    preview: 'What's the best thing about Switzerland?',
 *    value: 'I don't know, but the flag is a big plus!',
 *    important: true
 *  })
 * }
 * ```
 *
 * Use this power responsibly! :)
 */
console.tron = reactotron

/**
 * We tell typescript about our dark magic
 *
 * You can also import Reactotron yourself from ./reactotronClient
 * and use it directly, like Reactotron.log('hello world')
 */
declare global {
  interface Console {
    /**
     * Reactotron client for logging, displaying, measuring performance, and more.
     * @see https://github.com/infinitered/reactotron
     * @example
     * if (__DEV__) {
     *  console.tron.display({
     *    name: 'JOKE',
     *    preview: 'What's the best thing about Switzerland?',
     *    value: 'I don't know, but the flag is a big plus!',
     *    important: true
     *  })
     * }
     */
    tron: typeof reactotron
  }
}

/**
 * Now that we've setup all our Reactotron configuration, let's connect!
 */
reactotron.connect()
