import { supabase } from "app/config/config.base"
import { ErrorType, ErrorExperience, reportSentryError } from "app/utils/crashReporting"

export interface SubscriptionData {
  id: string
  user_id: string
  household_id: string | null
  profile_type: 'user' | 'household'
  product_id: string
  started_on: string
  ended_on: string | null
  created_at: string
  updated_at: string
  products: {
    id: string
    name: string
    description: string
    merchant_id: string
    merchants: {
      id: string
      name: string
      categories: string[]
      merchant_group_id: string
      merchant_groups: {
        id: string
        name: string
        logo: {
          url: string
          width: number | null
          height: number | null
        } | null
      }
    }
    pricing_tiers: Array<{
      id: string
      name: string
      cost: number
      currency: string
      description: string | null
    }>
  }
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  code?: string
}

export class SubscriptionService {
  public async getUserSubscriptions(userId: string): Promise<ServiceResponse<SubscriptionData[]>> {
    try {
      if (__DEV__ && console.tron) {
        console.tron.log('📋 Fetching user subscriptions for:', userId)
      }

      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          id,
          user_id,
          product_id,
          started_on,
          ended_on,
          created_at,
          updated_at,
          products (
            id,
            name,
            description,
            merchants (
            id,
              name,
              merchant_groups (
                id,
                name,
                  merchant_categories (
                    name
                  ),
                logos (
                  url,
                  width,
                  height
                )
              )
            ),
            pricing_tiers (
              id,
              name,
              cost,
              currency,
              description
            )
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error

      const transformedData = (data || []).map(subscription => ({
        ...subscription,
        products: {
          ...subscription.products,
          merchants: {
            ...subscription.products.merchants,
            merchant_groups: {
              ...subscription.products.merchants.merchant_groups,
              logo: subscription.products.merchants.merchant_groups?.logos?.[0] || null
            }
          }
        }
      }))

      if (__DEV__ && console.tron) {
        console.tron.log('✅ Successfully fetched subscriptions:', transformedData.length)
      }

      return { success: true, data: transformedData }
    } catch (error) {
      const subscriptionError = error instanceof Error ? error : new Error(`Failed to fetch user subscriptions, ${JSON.stringify(error)}`)
      reportSentryError(subscriptionError, ErrorType.HANDLED, ErrorExperience.DataFetch)

      if (__DEV__ && console.tron) {
        console.tron.error('❌ Failed to fetch user subscriptions:', error)
      }

      return {
        success: false,
        error: subscriptionError.message,
        code: 'SUBSCRIPTION_FETCH_ERROR'
      }
    }
  }

  public async createUserSubscription(subscriptionData: {
    userId: string
    householdId?: string
    profileType: 'user' | 'household'
    productId: string
    startedOn: string
    endedOn?: string
  }): Promise<ServiceResponse<SubscriptionData>> {
    try {
      if (__DEV__ && console.tron) {
        console.tron.log('📋 Creating user subscription:', subscriptionData)
      }

      const { data, error } = await supabase
        .from('user_subscriptions')
        .insert([{
          user_id: subscriptionData.userId,
          household_id: subscriptionData.householdId || null,
          profile_type: subscriptionData.profileType,
          product_id: subscriptionData.productId,
          started_on: subscriptionData.startedOn,
          ended_on: subscriptionData.endedOn || null,
        }])
        .select(`
          id,
          user_id,
          product_id,
          started_on,
          ended_on,
          created_at,
          updated_at,
          products (
            id,
            name,
            description,
            merchant_id,
            merchants (
              id,
              name,
              categories,
              merchant_group_id,
              merchant_groups (
                id,
                name,
                logos (
                  url,
                  width,
                  height
                )
              )
            ),
            pricing_tiers (
              id,
              name,
              cost,
              currency,
              description
            )
          )
        `)
        .single()

      if (error) throw error

      const transformedData = {
        ...data,
        products: {
          ...data.products,
          merchants: {
            ...data.products.merchants,
            merchant_groups: {
              ...data.products.merchants.merchant_groups,
              logo: data.products.merchants.merchant_groups.logos?.[0] || null
            }
          }
        }
      }

      if (__DEV__ && console.tron) {
        console.tron.log('✅ Successfully created subscription:', transformedData.id)
      }

      return { success: true, data: transformedData }
    } catch (error) {
      const subscriptionError = error instanceof Error ? error : new Error('Failed to create user subscription')
      reportSentryError(subscriptionError, ErrorType.HANDLED, ErrorExperience.DataWrite)

      if (__DEV__ && console.tron) {
        console.tron.error('❌ Failed to create user subscription:', error)
      }

      return {
        success: false,
        error: subscriptionError.message,
        code: 'SUBSCRIPTION_CREATE_ERROR'
      }
    }
  }

  public async updateUserSubscription(
    subscriptionId: string,
    updates: Partial<{
      endedOn: string | null
      startedOn: string
    }>
  ): Promise<ServiceResponse<SubscriptionData>> {
    try {
      if (__DEV__ && console.tron) {
        console.tron.log('📋 Updating user subscription:', subscriptionId, updates)
      }

      const updateData: any = {}
      if (updates.endedOn !== undefined) updateData.ended_on = updates.endedOn
      if (updates.startedOn !== undefined) updateData.started_on = updates.startedOn

      const { data, error } = await supabase
        .from('user_subscriptions')
        .update(updateData)
        .eq('id', subscriptionId)
        .select(`
          id,
          user_id,
          product_id,
          started_on,
          ended_on,
          created_at,
          updated_at,
          products (
            id,
            name,
            description,
            merchant_id,
            merchants (
              id,
              name,
              categories,
              merchant_group_id,
              merchant_groups (
                id,
                name,
                logos (
                  url,
                  width,
                  height
                )
              )
            ),
            pricing_tiers (
              id,
              name,
              cost,
              currency,
              description
            )
          )
        `)
        .single()

      if (error) throw error

      const transformedData = {
        ...data,
        products: {
          ...data.products,
          merchants: {
            ...data.products.merchants,
            merchant_groups: {
              ...data.products.merchants.merchant_groups,
              logo: data.products.merchants.merchant_groups?.logos?.[0] || null
            }
          }
        }
      }

      if (__DEV__ && console.tron) {
        console.tron.log('✅ Successfully updated subscription:', transformedData.id)
      }

      return { success: true, data: transformedData }
    } catch (error) {
      const subscriptionError = error instanceof Error ? error : new Error('Failed to update user subscription')
      reportSentryError(subscriptionError, ErrorType.HANDLED, ErrorExperience.DataWrite)

      if (__DEV__ && console.tron) {
        console.tron.error('❌ Failed to update user subscription:', error)
      }

      return {
        success: false,
        error: subscriptionError.message,
        code: 'SUBSCRIPTION_UPDATE_ERROR'
      }
    }
  }
}

export const subscriptionService = new SubscriptionService()
