/**
 * NotificationService - Core service for managing Expo Notifications
 * Handles permissions, tokens, local/push notifications, and Supabase integration
 */

import * as Notifications from 'expo-notifications'
import * as Device from 'expo-device'
import { Platform } from 'react-native'
import { supabase } from 'app/config/config.base'
import { ErrorType, ErrorExperience, reportSentryError } from 'app/utils/crashReporting'
import {
  NotificationTarget,
  CreateNotificationTargetInput,
  LocalNotificationRequest,
  NotificationSettings,
  NotificationServiceInitResult,
  ServiceResponse,
  NotificationError,
  NOTIFICATION_ERROR_CODES,
  ANDROID_CHANNELS,
  DeviceType,
  NotificationAction,
} from 'app/types/notification.types'

/**
 * Core notification service class
 * Singleton pattern to ensure consistent state across the app
 */
class NotificationService {
  private static instance: NotificationService
  private isInitialized = false
  private currentPushToken: string | null = null
  private notificationListener: any = null
  private responseListener: any = null

  private constructor() {
    // Configure notification behavior
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowBanner: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    })
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  /**
   * Initialize the notification service
   * Sets up permissions, channels, and listeners
   */
  public async initialize(userId?: string): Promise<NotificationServiceInitResult> {
    try {

      if (__DEV__) {
        console.tron.log('🔔 Initializing NotificationService...')
      }

      // Setup Android notification channels
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels()
      }

      // Request permissions
      const permissions = await this.requestPermissions()
      if (!permissions.granted) {
        return {
          success: false,
          error: 'Notification permissions not granted',
          permissions
        }
      }

      // Get push token
      const pushToken = await this.getPushToken()
      if (!pushToken) {
        return {
          success: false,
          error: 'Failed to get push token',
          permissions
        }
      }

      this.currentPushToken = pushToken

      // Register token with Supabase if user is provided
      if (userId) {
        await this.registerPushToken(userId, pushToken)
      }

      // Setup notification listeners
      this.setupNotificationListeners()

      this.isInitialized = true
      if (__DEV__) {
        console.tron.log('✅ NotificationService initialized successfully')
      }

      return {
        success: true,
        pushToken,
        permissions
      }
    } catch (error) {
      const notificationError = error instanceof Error ? error : new Error('Unknown initialization error')
      reportSentryError(notificationError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ NotificationService initialization failed:', error)
      }

      return {
        success: false,
        error: notificationError.message
      }
    }
  }

  /**
   * Request notification permissions
   */
  public async requestPermissions(): Promise<NotificationSettings> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync()
      let finalStatus = existingStatus

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync()
        finalStatus = status
      }

      const settings = await Notifications.getPermissionsAsync()

      return {
        granted: finalStatus === 'granted',
        ios: Platform.OS === 'ios' ? {
          status: finalStatus as any,
          allowsAlert: settings.ios?.allowsAlert ?? false,
          allowsBadge: settings.ios?.allowsBadge ?? false,
          allowsSound: settings.ios?.allowsSound ?? false,
          allowsCriticalAlerts: settings.ios?.allowsCriticalAlerts ?? false,
          allowsAnnouncements: settings.ios?.allowsAnnouncements ?? false,
          allowsDisplayInNotificationCenter: settings.ios?.allowsDisplayInNotificationCenter ?? false,
          allowsDisplayInCarPlay: settings.ios?.allowsDisplayInCarPlay ?? false,
          allowsDisplayOnLockScreen: settings.ios?.allowsDisplayOnLockScreen ?? false,
        } : undefined,
        android: Platform.OS === 'android' ? {
          status: finalStatus as any,
          importance: settings.android?.importance ?? 0,
        } : undefined
      }
    } catch (error) {
      throw new NotificationError(
        'Failed to request permissions',
        NOTIFICATION_ERROR_CODES.PERMISSION_DENIED,
        error
      )
    }
  }

  /**
   * Get Expo push token
   */
  public async getPushToken(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        if (__DEV__) {
          console.tron.warn('⚠️ Push notifications only work on physical devices')
        }
        return null
      }
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PUBLIC_PROJECT_ID
      })
      return token.data
    } catch (error) {
      console.debug("Failed to get push token:", error)
      const tokenError = new NotificationError(
        'Failed to get push token',
        NOTIFICATION_ERROR_CODES.TOKEN_REGISTRATION_FAILED,
        error
      )
      reportSentryError(tokenError, ErrorType.HANDLED, ErrorExperience.Notifications)
      throw tokenError
    }
  }

  /**
   * Setup Android notification channels
   */
  private async setupAndroidChannels(): Promise<void> {
    if (Platform.OS !== 'android') return

    try {
      for (const [channelType, config] of Object.entries(ANDROID_CHANNELS)) {
        await Notifications.setNotificationChannelAsync(config.id, {
          name: config.name,
          description: config.description,
          importance: config.importance as any,
          sound: config.sound,
          vibrationPattern: config.vibrate ? [0, 250, 250, 250] : undefined,
          showBadge: config.showBadge,
          lightColor: config.lightColor,
          enableLights: config.lights
        })
        if (__DEV__) {
          console.tron.log(`📱 Created Android channel: ${config.name}`)
        }
      }
    } catch (error) {
      const channelError = new NotificationError(
        'Failed to setup Android channels',
        NOTIFICATION_ERROR_CODES.CHANNEL_CREATION_FAILED,
        error
      )
      reportSentryError(channelError, ErrorType.HANDLED, ErrorExperience.Notifications)
      throw channelError
    }
  }

  /**
   * Setup notification event listeners
   */
  private setupNotificationListeners(): void {
    // Listener for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('🔔 Notification received:', notification)
      this.handleNotificationReceived(notification)
    })

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('👆 Notification response:', response)
      this.handleNotificationResponse(response)
    })
  }

  /**
   * Handle notification received while app is active
   */
  private handleNotificationReceived(notification: Notifications.Notification): void {
    if (__DEV__) {
      console.tron.log('🔔 Notification received:', notification)
    }

    // Extract custom actions from notification data
    const actions = notification.request.content.data?.actions as NotificationAction[] | undefined

    if (actions && actions.length > 0) {
      // Execute custom actions
      this.executeNotificationActions(actions)
    }
  }

  /**
   * Handle user interaction with notification
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    if (__DEV__) {
      console.tron.log('👆 Notification response:', response)
    }

    const notification = response.notification
    const actions = notification.request.content.data?.actions as NotificationAction[] | undefined

    if (actions && actions.length > 0) {
      // Execute custom actions when user taps notification
      this.executeNotificationActions(actions)
    }
  }

  /**
   * Execute custom notification actions
   */
  private async executeNotificationActions(actions: NotificationAction[]): Promise<void> {
    for (const action of actions) {
      try {
        await this.executeAction(action)
      } catch (error) {
        const actionError = error instanceof Error ? error : new Error('Failed to execute notification action')
        reportSentryError(actionError, ErrorType.HANDLED, ErrorExperience.Notifications)

        if (__DEV__) {
          console.tron.error('❌ Failed to execute notification action:', error)
        }
      }
    }
  }

  /**
   * Execute a single notification action
   */
  private async executeAction(action: NotificationAction): Promise<void> {
    switch (action.type) {
      case 'navigate':
        // Navigation will be handled by the hook/component
        if (__DEV__) {
          console.tron.log('🧭 Navigation action:', action.payload)
        }
        break

      case 'api_call':
        // Execute API call
        if (__DEV__) {
          console.tron.log('🌐 API call action:', action.payload)
        }
        break

      case 'update_state':
        // State update will be handled by the hook/component
        if (__DEV__) {
          console.tron.log('🔄 State update action:', action.payload)
        }
        break

      case 'refresh_data':
        // Data refresh will be handled by the hook/component
        if (__DEV__) {
          console.tron.log('🔄 Refresh data action:', action.payload)
        }
        break

      default:
        if (__DEV__) {
          console.tron.log('🎯 Custom action:', action)
        }
        break
    }
  }

  /**
   * Get current push token
   */
  public getCurrentPushToken(): string | null {
    return this.currentPushToken
  }

  /**
   * Check if service is initialized
   */
  public isServiceInitialized(): boolean {
    return this.isInitialized
  }

  /**
   * Register push token with Supabase
   */
  public async registerPushToken(
    userId: string,
    pushToken: string,
    deviceInfo?: any
  ): Promise<ServiceResponse<NotificationTarget>> {
    try {
      const deviceType = this.getDeviceType()
      const defaultDeviceInfo = {
        device_name: Device.deviceName || 'Unknown Device',
        os_version: Device.osVersion || 'Unknown',
        device_model: Device.modelName || 'Unknown',
        app_version: '1.0.0', // You can get this from app.json or Constants
        ...deviceInfo
      }

      const input: CreateNotificationTargetInput = {
        user_id: userId,
        push_token: pushToken,
        device_type: deviceType,
        device_info: defaultDeviceInfo
      }

      // First, try to update existing record
      const { data: existingTarget, error: fetchError } = await supabase
        .from('notification_targets')
        .select('*')
        .eq('user_id', userId)
        .eq('device_type', deviceType)
        .eq('push_token', pushToken)
        .single()

      if (existingTarget && !fetchError) {
        // Update existing record
        const { data, error } = await supabase
          .from('notification_targets')
          .update({
            device_info: defaultDeviceInfo,
            is_active: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingTarget.id)
          .select()
          .single()

        if (error) throw error
        return { success: true, data }
      } else {
        // Create new record
        const { data, error } = await supabase
          .from('notification_targets')
          .insert(input)
          .select()
          .single()

        if (error) throw error
        return { success: true, data }
      }
    } catch (error) {
      console.error('❌ Failed to register push token:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: NOTIFICATION_ERROR_CODES.SUPABASE_ERROR
      }
    }
  }





  /**
   * Register push token with Supabase
   */
  public async registerPushToken(
    userId: string,
    pushToken: string,
    deviceInfo?: any
  ): Promise<ServiceResponse<NotificationTarget>> {
    try {
      const deviceType = this.getDeviceType()
      const defaultDeviceInfo = {
        device_name: Device.deviceName || 'Unknown Device',
        os_version: Device.osVersion || 'Unknown',
        device_model: Device.modelName || 'Unknown',
        app_version: '1.0.0', // You can get this from app.json or Constants
        ...deviceInfo
      }

      const input: CreateNotificationTargetInput = {
        user_id: userId,
        push_token: pushToken,
        device_type: deviceType,
        device_info: defaultDeviceInfo
      }

      // First, try to update existing record
      const { data: existingTarget, error: fetchError } = await supabase
        .from('notification_targets')
        .select('*')
        .eq('user_id', userId)
        .eq('device_type', deviceType)
        .eq('push_token', pushToken)
        .single()

      if (existingTarget && !fetchError) {
        // Update existing record
        const { data, error } = await supabase
          .from('notification_targets')
          .update({
            device_info: defaultDeviceInfo,
            is_active: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingTarget.id)
          .select()
          .single()

        if (error) throw error
        return { success: true, data }
      } else {
        // Create new record
        const { data, error } = await supabase
          .from('notification_targets')
          .insert(input)
          .select()
          .single()

        if (error) throw error
        return { success: true, data }
      }
    } catch (error) {
      const registrationError = error instanceof Error ? error : new Error('Failed to register push token')
      reportSentryError(registrationError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ Failed to register push token:', error)
      }

      return {
        success: false,
        error: registrationError.message,
        code: NOTIFICATION_ERROR_CODES.SUPABASE_ERROR
      }
    }
  }

  /**
   * Send local notification
   */
  public async sendLocalNotification(request: LocalNotificationRequest): Promise<ServiceResponse<string>> {
    try {
      const notificationRequest: Notifications.NotificationRequestInput = {
        identifier: request.identifier,
        content: {
          title: request.content.title,
          body: request.content.body,
          data: request.content.data || {},
          sound: request.content.sound,
          badge: request.content.badge,
          categoryIdentifier: request.content.categoryIdentifier
        },
        trigger: this.createNotificationTrigger(request.trigger)
      }

      const identifier = await Notifications.scheduleNotificationAsync(notificationRequest)
      return { success: true, data: identifier }
    } catch (error) {
      const notificationError = error instanceof Error ? error : new Error('Failed to send local notification')
      reportSentryError(notificationError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ Failed to send local notification:', error)
      }

      return {
        success: false,
        error: notificationError.message,
        code: NOTIFICATION_ERROR_CODES.NOTIFICATION_SEND_FAILED
      }
    }
  }

  /**
   * Create notification trigger based on type
   */
  private createNotificationTrigger(trigger: LocalNotificationRequest['trigger']): any {
    switch (trigger.type) {
      case 'immediate':
        return null
      case 'date':
        return { date: trigger.date }
      case 'interval':
        return {
          seconds: trigger.seconds || 1,
          repeats: trigger.repeats || false
        }
      default:
        return null
    }
  }

  /**
   * Get device type
   */
  private getDeviceType(): DeviceType {
    if (Platform.OS === 'ios') return 'ios'
    if (Platform.OS === 'android') return 'android'
    return 'web'
  }

  /**
   * Cleanup listeners
   */
  public cleanup(): void {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener)
      this.notificationListener = null
    }

    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener)
      this.responseListener = null
    }

    this.isInitialized = false
    if (__DEV__) {
      console.tron.log('🧹 NotificationService cleaned up')
    }
  }

  /**
   * Update notification preferences
   */
  public async updateNotificationPreferences(
    userId: string,
    preferences: Partial<NotificationTarget['notification_preferences']>
  ): Promise<ServiceResponse<NotificationTarget[]>> {
    try {
      const { data, error } = await supabase
        .from('notification_targets')
        .update({
          notification_preferences: preferences,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('is_active', true)
        .select()

      if (error) throw error
      return { success: true, data }
    } catch (error) {
      const preferencesError = error instanceof Error ? error : new Error('Failed to update notification preferences')
      reportSentryError(preferencesError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ Failed to update notification preferences:', error)
      }

      return {
        success: false,
        error: preferencesError.message,
        code: NOTIFICATION_ERROR_CODES.SUPABASE_ERROR
      }
    }
  }

  /**
   * Get user's notification targets
   */
  public async getUserNotificationTargets(userId: string): Promise<ServiceResponse<NotificationTarget[]>> {
    try {
      const { data, error } = await supabase
        .from('notification_targets')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { success: true, data: data || [] }
    } catch (error) {
      const targetsError = error instanceof Error ? error : new Error('Failed to get notification targets')
      reportSentryError(targetsError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ Failed to get notification targets:', error)
      }

      return {
        success: false,
        error: targetsError.message,
        code: NOTIFICATION_ERROR_CODES.SUPABASE_ERROR
      }
    }
  }

  /**
   * Deactivate push token
   */
  public async deactivatePushToken(pushToken: string): Promise<ServiceResponse<void>> {
    try {
      const { error } = await supabase
        .from('notification_targets')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('push_token', pushToken)

      if (error) throw error
      return { success: true }
    } catch (error) {
      const deactivationError = error instanceof Error ? error : new Error('Failed to deactivate push token')
      reportSentryError(deactivationError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ Failed to deactivate push token:', error)
      }

      return {
        success: false,
        error: deactivationError.message,
        code: NOTIFICATION_ERROR_CODES.SUPABASE_ERROR
      }
    }
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance()
