import {createClient} from "@supabase/supabase-js";
import 'react-native-url-polyfill/auto'
import {LargeSecureStorage} from "app/utils/storage/LargeSecureStorage";


export interface ConfigBaseProps {
  persistNavigation: "always" | "dev" | "prod" | "never"
  catchErrors: "always" | "dev" | "prod" | "never"
  exitRoutes: string[],
  maxProfileQuestionQueueDepth:number,
  minPendingQuestionsRefreshTime: number
}

export type PersistNavigationConfig = ConfigBaseProps["persistNavigation"]

const BaseConfig: ConfigBaseProps = {
  // This feature is particularly useful in development mode, but
  // can be used in production as well if you prefer.
  persistNavigation: "dev",

  /**
   * Only enable if we're catching errors in the right environment
   */
  catchErrors: "always",

  /**
   * This is a list of all the route names that will exit the app if the back button
   * is pressed while in that screen. Only affects Android.
   */
  exitRoutes: ["Welcome"],
  maxProfileQuestionQueueDepth:10,
  minPendingQuestionsRefreshTime: (60 * 60 * 1000) // 1 hour
}

export const supabase = createClient(
    process.env.EXPO_PUBLIC_SUPABASE_URL || "",
    process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "",
    {
      auth: {
        storage: new LargeSecureStorage(),
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
      },
    })

export default BaseConfig
