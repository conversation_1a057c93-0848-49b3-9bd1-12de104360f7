import { MaterialCommunityIcons } from "@expo/vector-icons"

/**
 * Get the appropriate MaterialCommunityIcons currency icon for a given currency code
 * @param currency - The currency code (e.g., 'EUR', 'USD', 'GBP')
 * @returns The icon name for MaterialCommunityIcons
 */
export const getCurrencyIcon = (currency: string): keyof typeof MaterialCommunityIcons.glyphMap => {
  switch (currency?.toUpperCase()) {
    case "EUR":
      return "currency-eur"
    case "GBP":
      return "currency-gbp"
    case "USD":
    default:
      return "currency-usd"
  }
}

/**
 * Format a cost value with currency symbol
 * @param cost - The cost amount
 * @param currency - The currency code
 * @returns Formatted cost string
 */
export const formatCost = (cost: number | string, currency: string = 'USD'): string => {
  const numericCost = typeof cost === 'string' ? parseFloat(cost) : cost
  if (isNaN(numericCost)) return '0.00'
  
  return `${currency} ${numericCost.toFixed(2)}`
}

/**
 * Get currency symbol for display
 * @param currency - The currency code
 * @returns Currency symbol
 */
export const getCurrencySymbol = (currency: string): string => {
  switch (currency?.toUpperCase()) {
    case "EUR":
      return "€"
    case "GBP":
      return "£"
    case "USD":
    default:
      return "$"
  }
}
