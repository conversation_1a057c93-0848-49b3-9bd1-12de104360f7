
import * as Sentry from "@sentry/react-native"

export const initCrashReporting = () => {
  console.tron.debug("Initializing crash reporting")
  Sentry.init({
    dsn: "https://<EMAIL>:443/errortracking/api/v1/projects/62427896",
    tracesSampleRate: 1.0,
    // @ts-ignore
    profilesSampleRate: 1.0,
  });
}

/**
 * Error classifications used to sort errors on error reporting services.
 */
export enum ErrorType {
  /**
   * An error that would normally cause a red screen in dev
   * and force the user to sign out and restart.
   */
  FATAL = "Fatal",
  /**
   * An error caught by try/catch where defined using Reactotron.tron.error.
   */
  HANDLED = "Handled",
}

export enum ErrorExperience {
  PendingQuestions = "Pending Questions",
  Notifications = "Notifications"
}

/**
 * Manually report a handled error.
 */
export const reportSentryError = (error: Error, type: ErrorType = ErrorType.FATAL, errorExperience: ErrorExperience) => {
  if (__DEV__) {const message = error.message || "Unknown"
    console.error(error, message, type)
  } else {
    Sentry.captureException(error, { tags: { type, errorExperience } });
  }
}
