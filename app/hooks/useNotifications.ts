/**
 * useNotifications Hook - React hook for easy notification integration
 * Provides a simple interface for components to interact with the notification system
 */

import { useEffect, useState, useCallback, useRef } from 'react'
import { AppState, AppStateStatus } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { useAuth } from 'app/contexts/AuthContext'
import { useStores } from 'app/models'

// Lazy import to avoid startup issues
const getNotificationService = () => {
  try {
    const { notificationService } = require('app/services/NotificationService')
    return notificationService
  } catch (error) {
    if (__DEV__ && console.tron) {
      console.tron.error("Failed to import notification service:", error)
    }
    return null
  }
}
import {
  NotificationSettings,
  LocalNotificationRequest,
  NotificationTarget,
  NotificationAction,
  NavigationActionPayload,
  StateUpdateActionPayload,
  ApiCallActionPayload,
  NotificationChannelType
} from 'app/types/notification.types'

/**
 * Hook configuration options
 */
interface UseNotificationsOptions {
  autoInitialize?: boolean
  handleCustomActions?: boolean
  enableLogging?: boolean
}

/**
 * Hook return type
 */
interface UseNotificationsReturn {
  // State
  isInitialized: boolean
  isLoading: boolean
  permissions: NotificationSettings | null
  pushToken: string | null
  error: string | null

  // Actions
  initialize: () => Promise<boolean>
  requestPermissions: () => Promise<NotificationSettings>
  sendLocalNotification: (request: LocalNotificationRequest) => Promise<string | null>
  updatePreferences: (preferences: any) => Promise<boolean>
  getUserTargets: () => Promise<NotificationTarget[]>

  // Utility
  clearError: () => void
  refresh: () => Promise<void>
}

/**
 * Custom hook for notification management
 */
export function useNotifications(options: UseNotificationsOptions = {}): UseNotificationsReturn {
  const {
    autoInitialize = true,
    handleCustomActions = true,
    enableLogging = __DEV__
  } = options

  // Dependencies
  const navigation = useNavigation()
  const { userId } = useAuth()
  const rootStore = useStores()

  // State
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [permissions, setPermissions] = useState<NotificationSettings | null>(null)
  const [pushToken, setPushToken] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Refs for cleanup
  const appStateRef = useRef<AppStateStatus>()
  const initializationRef = useRef<boolean>(false)

  /**
   * Log helper
   */
  const log = useCallback((message: string, ...args: any[]) => {
    if (enableLogging && __DEV__) {
      console.tron.log(`🔔 useNotifications: ${message}`, ...args)
    }
  }, [enableLogging])

  /**
   * Initialize notification service
   */
  const initialize = useCallback(async (): Promise<boolean> => {
    if (initializationRef.current || !userId) {
      return isInitialized
    }

    setIsLoading(true)
    setError(null)
    initializationRef.current = true

    try {
      log('Initializing notification service...')

      const notificationService = getNotificationService()
      if (!notificationService) {
        setError('Notification service not available')
        log('Notification service not available')
        return false
      }

      const result = await notificationService.initialize(userId)

      if (result.success) {
        setIsInitialized(true)
        setPermissions(result.permissions || null)
        setPushToken(result.pushToken || null)
        log('Notification service initialized successfully')
        return true
      } else {
        setError(result.error || 'Failed to initialize notifications')
        log('Failed to initialize notification service:', result.error)
        return false
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      log('Error initializing notification service:', err)
      return false
    } finally {
      setIsLoading(false)
      initializationRef.current = false
    }
  }, [userId, isInitialized, log])

  /**
   * Request notification permissions
   */
  const requestPermissions = useCallback(async (): Promise<NotificationSettings> => {
    setIsLoading(true)
    setError(null)

    try {
      log('Requesting notification permissions...')

      const notificationService = getNotificationService()
      if (!notificationService) {
        const error = new Error('Notification service not available')
        setError(error.message)
        throw error
      }

      const newPermissions = await notificationService.requestPermissions()
      setPermissions(newPermissions)
      log('Permissions updated:', newPermissions)
      return newPermissions
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to request permissions'
      setError(errorMessage)
      log('Error requesting permissions:', err)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [log])

  /**
   * Send local notification
   */
  const sendLocalNotification = useCallback(async (request: LocalNotificationRequest): Promise<string | null> => {
    setError(null)

    try {
      log('Sending local notification:', request.content.title)

      const notificationService = getNotificationService()
      if (!notificationService) {
        setError('Notification service not available')
        log('Notification service not available')
        return null
      }

      const result = await notificationService.sendLocalNotification(request)

      if (result.success) {
        log('Local notification sent successfully:', result.data)
        return result.data || null
      } else {
        setError(result.error || 'Failed to send notification')
        log('Failed to send local notification:', result.error)
        return null
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      log('Error sending local notification:', err)
      return null
    }
  }, [log])

  /**
   * Update notification preferences
   */
  const updatePreferences = useCallback(async (preferences: any): Promise<boolean> => {
    if (!userId) {
      setError('User not authenticated')
      return false
    }

    setIsLoading(true)
    setError(null)

    try {
      log('Updating notification preferences:', preferences)

      const notificationService = getNotificationService()
      if (!notificationService) {
        setError('Notification service not available')
        log('Notification service not available')
        return false
      }

      const result = await notificationService.updateNotificationPreferences(userId, preferences)

      if (result.success) {
        log('Preferences updated successfully')
        return true
      } else {
        setError(result.error || 'Failed to update preferences')
        log('Failed to update preferences:', result.error)
        return false
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      log('Error updating preferences:', err)
      return false
    } finally {
      setIsLoading(false)
    }
  }, [userId, log])

  /**
   * Get user's notification targets
   */
  const getUserTargets = useCallback(async (): Promise<NotificationTarget[]> => {
    if (!userId) {
      setError('User not authenticated')
      return []
    }

    setError(null)

    try {
      log('Fetching user notification targets...')

      const notificationService = getNotificationService()
      if (!notificationService) {
        setError('Notification service not available')
        log('Notification service not available')
        return []
      }

      const result = await notificationService.getUserNotificationTargets(userId)

      if (result.success) {
        log('Fetched notification targets:', result.data?.length)
        return result.data || []
      } else {
        setError(result.error || 'Failed to fetch targets')
        log('Failed to fetch notification targets:', result.error)
        return []
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      log('Error fetching notification targets:', err)
      return []
    }
  }, [userId, log])

  /**
   * Execute custom notification actions
   */
  const executeCustomAction = useCallback(async (action: NotificationAction): Promise<void> => {
    if (!handleCustomActions) return

    try {
      log('Executing custom action:', action.type)

      switch (action.type) {
        case 'navigate':
          const navPayload = action.payload as NavigationActionPayload
          navigation.navigate(navPayload.screen as never, navPayload.params as never)
          break

        case 'update_state':
          const statePayload = action.payload as StateUpdateActionPayload
          // Access the appropriate store and call the action
          if (rootStore[statePayload.store as keyof typeof rootStore]) {
            const store = rootStore[statePayload.store as keyof typeof rootStore] as any
            if (store[statePayload.action]) {
              store[statePayload.action](statePayload.payload)
            }
          }
          break

        case 'api_call':
          const apiPayload = action.payload as ApiCallActionPayload
          // Execute API call (you can integrate with your API service here)
          log('API call action:', apiPayload)
          break

        case 'refresh_data':
          // Trigger data refresh
          await refresh()
          break

        default:
          log('Unknown action type:', action.type)
          break
      }
    } catch (err) {
      log('Error executing custom action:', err)
    }
  }, [handleCustomActions, navigation, rootStore, log])

  /**
   * Refresh notification data
   */
  const refresh = useCallback(async (): Promise<void> => {
    if (!isInitialized || !userId) return

    try {
      log('Refreshing notification data...')
      const targets = await getUserTargets()
      log('Refreshed notification targets:', targets.length)
    } catch (err) {
      log('Error refreshing notification data:', err)
    }
  }, [isInitialized, userId, getUserTargets, log])

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  /**
   * Handle app state changes
   */
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appStateRef.current?.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground
        if (isInitialized) {
          refresh()
        }
      }
      appStateRef.current = nextAppState
    }

    const subscription = AppState.addEventListener('change', handleAppStateChange)
    return () => subscription?.remove()
  }, [isInitialized, refresh])

  /**
   * Auto-initialize when user is available
   */
  useEffect(() => {
    if (autoInitialize && userId && !isInitialized && !initializationRef.current) {
      initialize()
    }
  }, [autoInitialize, userId, isInitialized, initialize])

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      // Cleanup is handled by the service singleton
      log('Hook cleanup')
    }
  }, [log])

  return {
    // State
    isInitialized,
    isLoading,
    permissions,
    pushToken,
    error,

    // Actions
    initialize,
    requestPermissions,
    sendLocalNotification,
    updatePreferences,
    getUserTargets,

    // Utility
    clearError,
    refresh
  }
}

/**
 * Convenience hook for sending quick notifications
 */
export function useQuickNotification() {
  const { sendLocalNotification } = useNotifications({ autoInitialize: false })

  const sendQuickNotification = useCallback(
    (title: string, body: string, channelType: NotificationChannelType = 'other', data?: any) => {
      return sendLocalNotification({
        content: {
          title,
          body,
          data: { channelType, ...data }
        },
        trigger: { type: 'immediate' }
      })
    },
    [sendLocalNotification]
  )

  return { sendQuickNotification }
}
