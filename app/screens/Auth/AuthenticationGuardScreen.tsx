import React, { FC } from "react"
import { observer } from "mobx-react-lite"
import { AppStackScreenProps } from "../../navigators/AppNavigator"
import { useStores } from "../../models"
import {AuthenticationScreen} from "app/screens";

interface AuthenticationGuardScreenProps extends AppStackScreenProps<"AuthenticationGuard"> {
    children: React.ReactNode
}

export const AuthenticationGuardScreen: FC<AuthenticationGuardScreenProps> = observer(function AuthenticationGuardScreen(props: AuthenticationGuardScreenProps) {

    // @ts-ignore
    const { auth } = useStores()

    if (auth.isAuthenticated || auth.isRegistering) {
        return props.children
    }

    // If user is registering or not authenticated, show the authentication screen
    return (
    <AuthenticationScreen />
  )
})

