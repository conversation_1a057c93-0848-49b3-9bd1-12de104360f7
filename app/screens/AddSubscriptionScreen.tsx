import React, { FC, useState, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { View, StyleSheet, ScrollView, Alert } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text, DatePickerField, LoadingButton, ServiceSelectionCard } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"
import { subscriptionService } from "app/services/SubscriptionService"
import { ErrorType, ErrorExperience, reportSentryError } from "app/utils/crashReporting"

interface AddSubscriptionScreenProps extends AppStackScreenProps<"AddSubscription"> {}

export const AddSubscriptionScreen: FC<AddSubscriptionScreenProps> = observer(function AddSubscriptionScreen({
  navigation,
  route
}) {
  const rootStore = useStores()
  const { userId } = useAuth()
  const [selectedService, setSelectedService] = useState<any>(null)
  const [formData, setFormData] = useState({
    startDate: new Date(),
    billDate: new Date(),
  })
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (route.params?.selectedService) {
      // Check if selectedService is an object with id, name, and description
      if (typeof route.params.selectedService === 'object' && route.params.selectedService !== null) {
        const service = route.params.selectedService;
        console.log('Selected service object in useEffect:', service);
        setSelectedService(service);
      }
    }
  }, [route.params?.selectedService])



  const handleStartDateChange = (date: Date) => {
    setFormData(prev => ({ ...prev, startDate: date }))
  }

  const handleBillDateChange = (date: Date) => {
    setFormData(prev => ({ ...prev, billDate: date }))
  }

  const handleSaveSubscription = async () => {
    if (!userId || !selectedService) {
      Alert.alert('Error', 'Please select a service before saving.')
      return
    }

    setIsLoading(true)

    try {
      if (__DEV__ && console.tron) {
        console.tron.log('📋 Saving subscription with selectedService:', selectedService)
      }

      const response = await subscriptionService.createUserSubscription({
        userId,
        profileType: 'user',
        productId: selectedService.productId || selectedService.id,
        startedOn: formData.startDate.toISOString(),
        billDate: formData.billDate.toISOString(),
        pricingTierId: selectedService.pricingTierId,
      })

      if (response.success && response.data) {
        rootStore.bustSubscriptionsCache()

        if (__DEV__ && console.tron) {
          console.tron.log('✅ Subscription saved successfully:', response.data.id)
        }

        navigation.goBack()
      } else {
        throw new Error(response.error || 'Failed to save subscription')
      }
    } catch (error) {
      const subscriptionError = error instanceof Error ? error : new Error('Failed to save subscription')
      reportSentryError(subscriptionError, ErrorType.HANDLED, ErrorExperience.DataWrite)

      if (__DEV__ && console.tron) {
        console.tron.error('❌ Failed to save subscription:', error)
      }

      Alert.alert(
        'Error',
        'Failed to save subscription. Please try again.',
        [{ text: 'OK' }]
      )
    } finally {
      setIsLoading(false)
    }
  }

  const navigateToServiceSelection = () => {
    navigation.navigate("ServiceSelection", {
      onSelectService: (service: { id: string, name: string, description: string, cost?: number, currency?: string, productId?: string, merchantGroupLogo?: string }) => {
        console.log('Service selected in AddSubscriptionScreen:', service);
        setSelectedService(service);
      }
    });
  };


  return (
    <Screen preset="fixed" safeAreaEdges={["top"]} style={styles.screen}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <Text style={styles.title}>Add New Subscription</Text>

        <ServiceSelectionCard
          selectedService={selectedService}
          onPress={navigateToServiceSelection}
          style={styles.serviceCard}
        />

        <View style={styles.dateSection}>
          <Text style={styles.sectionTitle}>Subscription Details</Text>

          <DatePickerField
            label="Start Date"
            value={formData.startDate}
            onChange={handleStartDateChange}
            style={styles.dateField}
          />

          <DatePickerField
            label="Bill Date"
            value={formData.billDate}
            onChange={handleBillDateChange}
            style={styles.dateField}
          />
        </View>

        <View style={{ height: 80 }} />
      </ScrollView>

      <View style={styles.fixedButtonContainer}>
        <LoadingButton
          title="Save Subscription"
          onPress={handleSaveSubscription}
          loading={isLoading}
          disabled={!selectedService}
          loadingText="Saving..."
        />
      </View>
    </Screen>
  )
})

const styles = StyleSheet.create({
  screen: {
    flex: 1,
  },
  scrollContainer: {
    padding: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 24,
    fontFamily: typography.primary.bold,
    marginBottom: spacing.lg,
    textAlign: 'center',
    color: colors.text,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: typography.primary.semiBold,
    marginBottom: spacing.md,
    color: colors.text,
  },
  dateSection: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  dateField: {
    marginBottom: spacing.md,
  },
  serviceCard: {
    marginBottom: spacing.lg,
  },
  fixedButtonContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
  },
})