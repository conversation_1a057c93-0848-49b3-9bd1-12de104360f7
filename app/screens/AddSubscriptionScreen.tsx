import React, { FC, useState, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { View, StyleSheet, Pressable, ScrollView, Alert, ActivityIndicator } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text, Card, AutoImage } from "app/components"
import { colors, spacing, typography } from "app/theme"
import DateTimePicker from '@react-native-community/datetimepicker'
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"
import { subscriptionService } from "app/services/SubscriptionService"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faChevronRight, faCalendarAlt } from "@fortawesome/free-solid-svg-icons"
import { ErrorType, ErrorExperience, reportSentryError } from "app/utils/crashReporting"

interface AddSubscriptionScreenProps extends AppStackScreenProps<"AddSubscription"> {}

export const AddSubscriptionScreen: FC<AddSubscriptionScreenProps> = observer(function AddSubscriptionScreen({
  navigation,
  route
}) {
  const rootStore = useStores()
  const { userId } = useAuth()
  const [selectedService, setSelectedService] = useState<any>(null)
  const [formData, setFormData] = useState({
    startDate: new Date(),
    billDate: new Date(),
  })

  const [showStartDatePicker, setShowStartDatePicker] = useState(false)
  const [showBillDatePicker, setShowBillDatePicker] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (route.params?.selectedService) {
      // Check if selectedService is an object with id, name, and description
      if (typeof route.params.selectedService === 'object' && route.params.selectedService !== null) {
        const service = route.params.selectedService;
        console.log('Selected service object in useEffect:', service);
        setSelectedService(service);
      }
    }
  }, [route.params?.selectedService])



  const handleDateChange = (field: 'startDate' | 'billDate', selectedDate?: Date) => {
    if (selectedDate) {
      setFormData(prev => ({ ...prev, [field]: selectedDate }))
    }
    if (field === 'startDate') {
      setShowStartDatePicker(false)
    } else {
      setShowBillDatePicker(false)
    }
  }

  const handleSaveSubscription = async () => {
    if (!userId || !selectedService) {
      Alert.alert('Error', 'Please select a service before saving.')
      return
    }

    setIsLoading(true)

    try {
      if (__DEV__ && console.tron) {
        console.tron.log('📋 Saving subscription with selectedService:', selectedService)
      }

      const response = await subscriptionService.createUserSubscription({
        userId,
        profileType: 'user',
        productId: selectedService.productId || selectedService.id,
        startedOn: formData.startDate.toISOString(),
      })

      if (response.success && response.data) {
        rootStore.bustSubscriptionsCache()

        if (__DEV__ && console.tron) {
          console.tron.log('✅ Subscription saved successfully:', response.data.id)
        }

        navigation.goBack()
      } else {
        throw new Error(response.error || 'Failed to save subscription')
      }
    } catch (error) {
      const subscriptionError = error instanceof Error ? error : new Error('Failed to save subscription')
      reportSentryError(subscriptionError, ErrorType.HANDLED, ErrorExperience.DataWrite)

      if (__DEV__ && console.tron) {
        console.tron.error('❌ Failed to save subscription:', error)
      }

      Alert.alert(
        'Error',
        'Failed to save subscription. Please try again.',
        [{ text: 'OK' }]
      )
    } finally {
      setIsLoading(false)
    }
  }

  const navigateToServiceSelection = () => {
    navigation.navigate("ServiceSelection", {
      onSelectService: (service: { id: string, name: string, description: string, cost?: number, currency?: string, productId?: string, merchantGroupLogo?: string }) => {
        console.log('Service selected in AddSubscriptionScreen:', service);
        setSelectedService(service);
      }
    });
  };



  const renderServiceSelectionCard = () => {
    if (selectedService) {
      // Show selected service details
      return (
        <Card
          style={styles.serviceCard}
          onPress={navigateToServiceSelection}
          LeftComponent={
            <AutoImage
              source={{ uri: selectedService.merchantGroupLogo }}
              style={styles.cardLogo}
            />
          }
          content={(
            <View style={{flex:1}}>
              <Text preset={"bold"}>
                {selectedService.name}
              </Text>
              <Text preset={"formHelper"}>
                {selectedService.pricingTierName}
              </Text>
              <Text preset={"bold"}>
                €{selectedService.cost}
              </Text>
            </View>
          )}
        >
        </Card>
      );
    } else {
      // Show CTA to select product
      return (
        <Card
          style={styles.serviceCard}
          heading="Service"
          content="Select Product"
          onPress={navigateToServiceSelection}
          RightComponent={
            <FontAwesomeIcon icon={faChevronRight} size={16} color={colors.palette.neutral500} />
          }
        />
      );
    }
  }


  return (
    <Screen preset="fixed" safeAreaEdges={["top"]} style={styles.screen}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <Text style={styles.title}>Add New Subscription to Household</Text>

        {renderServiceSelectionCard()}

        <View style={styles.dateSection}>
          <Text style={styles.sectionTitle}>Subscription Details</Text>

          <View style={styles.dateInputContainer}>
            <FontAwesomeIcon icon={faCalendarAlt} size={16} color={colors.palette.primary500} />
            <Text style={styles.dateLabel}>Start Date</Text>
          </View>
          <Pressable
            style={styles.dateInput}
            onPress={() => setShowStartDatePicker(true)}
          >
            <Text style={styles.dateText}>{formData.startDate.toLocaleDateString()}</Text>
          </Pressable>
          {showStartDatePicker && (
            <DateTimePicker
              value={formData.startDate}
              mode="date"
              display="default"
              onChange={(_, date) => handleDateChange('startDate', date)}
            />
          )}
        </View>

        <View style={{ height: 80 }} />
      </ScrollView>

      <View style={styles.fixedButtonContainer}>
        <Pressable
          style={[
            styles.submitButton,
            (!selectedService || isLoading) && styles.submitButtonDisabled
          ]}
          onPress={handleSaveSubscription}
          disabled={!selectedService || isLoading}
        >
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="white" />
              <Text style={styles.loadingText}>Saving...</Text>
            </View>
          ) : (
            <Text style={[
              styles.submitButtonText,
              (!selectedService || isLoading) && styles.submitButtonTextDisabled
            ]}>
              Save Subscription
            </Text>
          )}
        </Pressable>
      </View>
    </Screen>
  )
})

const styles = StyleSheet.create({
  screen: {
    flex: 1,
  },
  scrollContainer: {
    padding: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 24,
    fontFamily: typography.primary.bold,
    marginBottom: spacing.lg,
    textAlign: 'center',
    color: colors.text,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: typography.primary.semiBold,
    marginBottom: spacing.md,
    color: colors.text,
  },
  dateSection: {
    backgroundColor: colors.palette.neutral100,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  dateLabel: {
    fontSize: 16,
    fontFamily: typography.primary.medium,
    marginLeft: spacing.xs,
    color: colors.text,
  },
  dateInput: {
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
    borderRadius: 8,
    padding: spacing.sm,
    backgroundColor: colors.palette.neutral50,
    minHeight: 48,
    justifyContent: 'center',
  },
  dateText: {
    fontSize: 16,
    fontFamily: typography.primary.normal,
    color: colors.text,
  },
  serviceCard: {
    marginBottom: spacing.lg,
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm
  },
  cardLogo: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems:"center",
    justifyContent:"center",
    alignSelf:"center"
  },
  cardFooterText: {
    flex: 1
  },
  cardPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.palette.primary600,
    marginBottom: spacing.xxs
  },
  cardDescription: {
    fontSize: 14,
    color: colors.palette.neutral600
  },
  submitButtonDisabled: {
    backgroundColor: colors.palette.neutral400,
  },
  submitButtonTextDisabled: {
    color: colors.palette.neutral600,
  },
  fixedButtonContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
  },
  submitButton: {
    backgroundColor: colors.tint,
    padding: spacing.md,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  submitButtonText: {
    color: colors.palette.neutral100,
    fontFamily: typography.primary.bold,
    fontSize: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.palette.neutral100,
    fontFamily: typography.primary.medium,
    fontSize: 16,
    marginLeft: spacing.xs,
  },
})