// AddSubscriptionScreen.tsx
import React, { FC, useState, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { View, StyleSheet, TextInput, Pressable, ScrollView, Image } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text, Card, AutoImage } from "app/components"
import { colors, spacing } from "app/theme"
import DateTimePicker from '@react-native-community/datetimepicker'
import { useStores } from "app/models" // Adjust path to your store hook
import { v4 as uuidv4 } from 'uuid'
import { UserSubscriptionModel } from "app/models/UserSubscription/UserSubscription" // Adjust path
import { useAuth } from "app/contexts/AuthContext"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faChevronRight } from "@fortawesome/free-solid-svg-icons"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import { supabase } from "app/config/config.base"

interface AddSubscriptionScreenProps extends AppStackScreenProps<"AddSubscription"> {}

export const AddSubscriptionScreen: FC<AddSubscriptionScreenProps> = observer(function AddSubscriptionScreen({
  navigation,
  route
}) {
  const rootStore = useStores()
  const [selectedService, setSelectedService] = useState<any>(null)
  const [formData, setFormData] = useState({
    billDate: new Date(),
    startDate: new Date(),
    endDate: new Date(new Date().getFullYear() + 1, new Date().getMonth(), new Date().getDate())
  })

  const [showStartDatePicker, setShowStartDatePicker] = useState(false)
  const [showEndDatePicker, setShowEndDatePicker] = useState(false)
  const [showBillDatePicker, setShowBillDatePicker] = useState(false)

  const userId = rootStore.auth.userId

  useEffect(() => {
    if (route.params?.selectedService) {
      // Check if selectedService is an object with id, name, and description
      if (typeof route.params.selectedService === 'object' && route.params.selectedService !== null) {
        const service = route.params.selectedService;
        console.log('Selected service object in useEffect:', service);
        setSelectedService(service);
      }
    }
  }, [route.params?.selectedService])



  const handleDateChange = (field: 'startDate' | 'endDate' | 'billDate', selectedDate?: Date) => {
    if (selectedDate) {
      setFormData(prev => ({ ...prev, [field]: selectedDate }))
    }
    if (field === 'startDate') {
      setShowStartDatePicker(false)
    } else if (field === 'endDate') {
      setShowEndDatePicker(false)
    } else {
      setShowBillDatePicker(false)
    }
  }

  const handleSaveSubscription = async () => {
    if (userId && selectedService) {
      console.log('Saving subscription with selectedService:', selectedService);
      const newSubscription = UserSubscriptionModel.create({
        id: uuidv4(),
        userId,
        profileType: 'user',
        productId: selectedService.productId || selectedService.id,
        startedOn: formData.startDate.toISOString(),
        endedOn: formData.endDate.toISOString(),
        billDate: formData.billDate.toISOString(),
      })

      const { data, error } = await supabase
        .from("user_subscriptions")
        .insert([
          {
            user_id: userId,
            product_id: selectedService.productId,
            started_on: formData.startDate.toISOString(),
            ended_on: formData.endDate.toISOString(),
            bill_date: formData.billDate.toISOString(),
            pricing_tier_id: selectedService.pricingTierId
          },
        ])

      rootStore.addSubscription(newSubscription)

      console.log('Subscription saved:', newSubscription)
      navigation.goBack()
    }
  }

  const navigateToServiceSelection = () => {
    navigation.navigate("ServiceSelection", {
      onSelectService: (service: { id: string, name: string, description: string, cost?: number, currency?: string, productId?: string, merchantGroupLogo?: string }) => {
        console.log('Service selected in AddSubscriptionScreen:', service);
        setSelectedService(service);
      }
    });
  };

  // Helper function to get the correct currency icon
  const getCurrencyIcon = (currency: string): keyof typeof MaterialCommunityIcons.glyphMap => {
    switch (currency) {
      case "EUR":
        return "currency-eur";
      case "GBP":
        return "currency-gbp";
      case "USD":
      default:
        return "currency-usd";
    }
  }

  const renderServiceSelectionCard = () => {
    if (selectedService) {
      // Show selected service details
      return (
        <Card
          style={styles.serviceCard}
          onPress={navigateToServiceSelection}
          LeftComponent={
            <AutoImage
              source={{ uri: selectedService.merchantGroupLogo }}
              style={styles.cardLogo}
            />
          }
          content={(
            <View style={{flex:1}}>
              <Text preset={"bold"}>
                {selectedService.name}
              </Text>
              <Text preset={"formHelper"}>
                {selectedService.pricingTierName}
              </Text>
              <Text preset={"bold"}>
                €{selectedService.cost}
              </Text>
            </View>
          )}
        >
        </Card>
      );
    } else {
      // Show CTA to select product
      return (
        <Card
          style={styles.serviceCard}
          heading="Service"
          content="Select Product"
          onPress={navigateToServiceSelection}
          RightComponent={
            <FontAwesomeIcon icon={faChevronRight} size={16} color={colors.palette.neutral500} />
          }
        />
      );
    }
  }


  return (
    <Screen preset="fixed" safeAreaEdges={["top"]} style={styles.screen}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <Text style={styles.title}>Add New Subscription to Household</Text>

        {renderServiceSelectionCard()}

        <Text style={styles.label}>Start Date</Text>
        <Pressable
          style={styles.input}
          onPress={() => setShowStartDatePicker(true)}
        >
          <Text>{formData.startDate.toLocaleDateString()}</Text>
        </Pressable>
        {showStartDatePicker && (
          <DateTimePicker
            value={formData.startDate}
            mode="date"
            display="default"
            onChange={(_, date) => handleDateChange('startDate', date)}
          />
        )}

        <Text style={styles.label}>End Date</Text>
        <Pressable
          style={styles.input}
          onPress={() => setShowEndDatePicker(true)}
        >
          <Text>{formData.endDate.toLocaleDateString()}</Text>
        </Pressable>
        {showEndDatePicker && (
          <DateTimePicker
            value={formData.endDate}
            mode="date"
            display="default"
            onChange={(_, date) => handleDateChange('endDate', date)}
          />
        )}

        <Text style={styles.label}>Bill Date</Text>
        <Pressable
          style={styles.input}
          onPress={() => setShowBillDatePicker(true)}
        >
          <Text>{formData.billDate.toLocaleDateString()}</Text>
        </Pressable>
        {showBillDatePicker && (
          <DateTimePicker
            value={formData.billDate}
            mode="date"
            display="default"
            onChange={(_, date) => handleDateChange('billDate', date)}
          />
        )}

        <View style={{ height: 80 }} />
      </ScrollView>

      <View style={styles.fixedButtonContainer}>
        <Pressable
          style={[styles.submitButton, !selectedService && styles.submitButtonDisabled]}
          onPress={handleSaveSubscription}
          disabled={!selectedService}
        >
          <Text style={[styles.submitButtonText, !selectedService && styles.submitButtonTextDisabled]}>Save Subscription</Text>
        </Pressable>
      </View>
    </Screen>
  )
})

const styles = StyleSheet.create({
  screen: {
    flex: 1,
  },
  scrollContainer: {
    padding: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: colors.text,
  },
  input: {
    borderWidth: 1,
    borderColor: '#000000',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    backgroundColor: 'white',
    fontSize: 16,
    height: 48,
    justifyContent: 'center',
  },
  serviceCard: {
    marginBottom: spacing.lg,
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm
  },
  cardLogo: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems:"center",
    justifyContent:"center",
    alignSelf:"center"
  },
  cardFooterText: {
    flex: 1
  },
  cardPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.palette.primary600,
    marginBottom: spacing.xxs
  },
  cardDescription: {
    fontSize: 14,
    color: colors.palette.neutral600
  },
  submitButtonDisabled: {
    backgroundColor: colors.palette.neutral400,
  },
  submitButtonTextDisabled: {
    color: colors.palette.neutral600,
  },
  fixedButtonContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
  },
  submitButton: {
    backgroundColor: colors.tint,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
})