import React, { FC } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, View, Pressable } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text } from "app/components"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faPlus } from "@fortawesome/free-solid-svg-icons"
import { colors, spacing } from "app/theme"
import { navigate } from "app/navigators"

interface UserSubscriptionsScreenProps extends AppStackScreenProps<"UserSubscriptions"> {}

export const UserSubscriptionsScreen: FC<UserSubscriptionsScreenProps> = observer(function UserSubscriptionsScreen() {
  return (
    <View style={$container}>
      <Screen style={$root} preset="scroll" contentContainerStyle={$scrollContainer}>
        <Text text="userSubscriptions" />

        {/* Add New Subscription Button - Fixed position */}
        <Pressable
          style={$addButton}
          onPress={() => navigate("AddSubscription")}
        >
          <FontAwesomeIcon icon={faPlus} color="white" size={16} />
        </Pressable>
      </Screen>
    </View>
  )
})

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $root: ViewStyle = {
  flex: 1,
}

const $scrollContainer: ViewStyle = {
  padding: spacing.md,
  paddingBottom: 80, // Space for bottom tab bar and add button
}

const $addButton: ViewStyle = {
  position: 'absolute',
  bottom: 80,
  right: 20,
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: colors.tint,
  paddingVertical: 12,
  paddingHorizontal: 16,
  borderRadius: 24,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
}
