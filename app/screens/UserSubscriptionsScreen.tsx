import React, { FC, useEffect, useCallback } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, View, Pressable } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, SubscriptionsList } from "app/components"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faPlus } from "@fortawesome/free-solid-svg-icons"
import { colors, spacing } from "app/theme"
import { navigate } from "app/navigators"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"

interface UserSubscriptionsScreenProps extends AppStackScreenProps<"UserSubscriptions"> {}

export const UserSubscriptionsScreen: FC<UserSubscriptionsScreenProps> = observer(function UserSubscriptionsScreen() {
  const rootStore = useStores()
  const { userId } = useAuth()

  useEffect(() => {
    if (userId) {
      rootStore.fetchUserSubscriptions(userId)
    }
  }, [userId])

  const handleRefresh = useCallback(() => {
    if (userId) {
      rootStore.fetchUserSubscriptions(userId, true)
    }
  }, [userId])

  const handleSubscriptionPress = useCallback((subscription: UserSubscription) => {
    if (__DEV__ && console.tron) {
      console.tron.log('📋 Subscription pressed:', subscription.merchantName)
    }
  }, [])

  const handleAddSubscription = useCallback(() => {
    rootStore.bustSubscriptionsCache()
    navigate("AddSubscription")
  }, [])

  return (
    <View style={$container}>
      <Screen style={$root} preset="fixed">
        <SubscriptionsList
          sections={rootStore.groupedSubscriptions}
          loading={rootStore.subscriptionsLoading}
          onRefresh={handleRefresh}
          onSubscriptionPress={handleSubscriptionPress}
        />

        <Pressable
          style={$addButton}
          onPress={handleAddSubscription}
        >
          <FontAwesomeIcon icon={faPlus} color="white" size={16} />
        </Pressable>
      </Screen>
    </View>
  )
})

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $root: ViewStyle = {
  flex: 1,
}

const $scrollContainer: ViewStyle = {
  padding: spacing.md,
  paddingBottom: 80, // Space for bottom tab bar and add button
}

const $addButton: ViewStyle = {
  position: 'absolute',
  bottom: 80,
  right: 20,
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: colors.tint,
  paddingVertical: 12,
  paddingHorizontal: 16,
  borderRadius: 24,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
}
