import React, { useRef } from "react"
import { observer } from "mobx-react-lite"
import { useStores } from "app/models"
import Onboarding from "react-native-onboarding-swiper"
import { AutoImage } from "app/components"
import { colors } from "app/theme"

import { Onboarding1 } from "./Steps/Onboarding1"
import { Onboarding2 } from "./Steps/Onboarding2"
import { Onboarding3 } from "./Steps/Onboarding3"
import { Onboarding4 } from "./Steps/Onboarding4"
import { Onboarding5 } from "./Steps/Onboarding5"
import { Hero1 } from "./Steps/Hero1"
import { Hero2 } from "./Steps/Hero2"
import { Hero3 } from "./Steps/Hero3"
import { Hero4 } from "./Steps/Hero4"
import { CelebrateScreen } from "./Steps/CelebrateScreen"

const OnboardingSwiper = observer(({children}) => {
  const { userState } = useStores()
  const onboardingRef = useRef(null)

  if (userState.hasCompletedOnboarding) {
    return <>{children}</>
  }

  return (
    <Onboarding
      ref={onboardingRef}
      showNext={false}
      showSkip={false}
      showDone={false}
      DotComponent={() => null}
      onDone={() => {
        userState.completeOnboarding()
      }}
      pages={[
        Onboarding1(),
        Onboarding2(),
        Onboarding3(),
        Onboarding4(),
        Onboarding5(onboardingRef),
        Hero1(),
        Hero2(),
        Hero3(),
        Hero4(),
        {
          backgroundColor: "#121212",
          image: <CelebrateScreen />,
          title: "",
          subtitle: "",
        },
        {
          backgroundColor: colors.background,
          image: (
            <AutoImage
              style={{
                borderColor: colors.border,
                opacity: 0.5,
                height: 240,
                aspectRatio: 1,
              }}
              source={require("../../../assets/images/questions.png")}
            />
          ),
          title: "Simple questions",
          subtitle:
            "Our AI asks simple questions to learn what you really need from your subscriptions",
        },
        {
          backgroundColor: colors.background,
          image: (
            <AutoImage
              style={{
                borderColor: colors.border,
                opacity: 0.5,
                height: 240,
                aspectRatio: 1,
              }}
              source={require("../../../assets/images/questions.png")}
            />
          ),
          title: "PW Extras",
          subtitle:
            "Our AI asks simple questions to learn what you really need from your subscriptions",
        },
      ]}
    />
  )
})

export const OneSubOnboarding = observer(({ children }: { children: React.ReactNode }) => {
  return <OnboardingSwiper>{children}</OnboardingSwiper>
})

export default OneSubOnboarding
