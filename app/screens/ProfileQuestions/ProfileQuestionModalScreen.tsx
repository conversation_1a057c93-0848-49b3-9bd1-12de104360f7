import React, { FC, useEffect, useMemo, useState } from "react"
import { observer } from "mobx-react-lite"
import { Pressable, View, ViewStyle } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Button, Screen, Text } from "app/components"
import { ProfileQuestion, ProfileQuestionAnswer, useStores } from "app/models"
import { colors } from "app/theme"
import { useNavigation } from "@react-navigation/native"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faSquare, faSquareCheck, faX } from "@fortawesome/free-solid-svg-icons"
import { useAuth } from "app/contexts/AuthContext"

interface ProfileQuestionModalScreenProps extends AppStackScreenProps<"ProfileQuestionModal"> {}

export const ProfileQuestionModalScreen: FC<ProfileQuestionModalScreenProps> = observer(
  function ProfileQuestionModalScreen() {
    const navigation = useNavigation()
    const rootStore = useStores()
    const { userId } = useAuth()
    const [selectedAnswers, setSelectedAnswers] = useState<ProfileQuestionAnswer[]>([])

    // Get current question and queue depth
    const queueDepth = useMemo(() => {
      return userId ? rootStore.pendingQuestions.getPendingQuestionCount("user", userId) : 0
    }, [userId])

    const questionInView: ProfileQuestion | undefined = useMemo(() => {
      return userId ? rootStore.pendingQuestions.peekOldestPendingQuestion("user", userId) : undefined
    }, [userId])

    useEffect(() => {
      return () => {
        if (userId) {
          rootStore.pendingQuestions.getPendingQuestionCount("user", userId)
        }
      }
    }, [])


    // Handle submission of answers
    const handleSubmitAnswers = () => {
      if (!userId || !questionInView) return

      // Here you would typically send the answers to your backend
      // await submitAnswersToBackend(questionInView.questionId, selectedAnswers)

      // Remove the question from the queue
      rootStore.pendingQuestions.removeOldestPendingQuestion("user", userId)

      // Navigate back
      navigation.goBack()
    }

    // Handle single answer selection
    const handleSingleAnswer = (answer: ProfileQuestionAnswer) => {
      if (!userId || !questionInView) return

      // For single select, we immediately submit the answer and remove the question
      // await submitAnswersToBackend(questionInView.questionId, [answer])

      rootStore.pendingQuestions.removeOldestPendingQuestion("user", userId)
      navigation.goBack()
    }

    const renderAnswers = (question: ProfileQuestion) => {
      if (question.allow_multi_select) {
        return (
          <View style={{ marginTop: 32 }}>
            <Text size="xxs" preset="formHelper">
              Select all that apply
            </Text>
            {question.options.map((option) => {
              const isSelected = selectedAnswers.find((x) => x.answer_text === option.answer_text)
              const onPressMethod = () => {
                if (isSelected) {
                  setSelectedAnswers(selectedAnswers.filter((x) => x.answer_text !== option.answer_text))
                } else {
                  setSelectedAnswers([...selectedAnswers, option])
                }
              }
              return (
                <Pressable onPress={onPressMethod} style={[$answer, $answerMulti]} key={option.answer_text}>
                  <FontAwesomeIcon
                    color={isSelected ? colors.text : "lightgrey"}
                    icon={isSelected ? faSquareCheck : faSquare}
                  />
                  <Text preset="subheading">{option.answer_text}</Text>
                </Pressable>
              )
            })}
          </View>
        )
      } else {
        return question.options.map((option) => {
          return (
            <Pressable onPress={() => handleSingleAnswer(option)} style={$answer} key={option.answer_text}>
              <Text preset="subheading">{option.answer_text}</Text>
            </Pressable>
          )
        })
      }
    }

    // Handle close modal
    const handleClose = () => {
      navigation.goBack()
    }

    return (
      <Screen style={$root} preset="scroll">
        <View style={$titleContainer}>
          <Text preset="heading">{questionInView?.question_text}</Text>
          <Pressable onPress={handleClose}>
            <FontAwesomeIcon icon={faX} />
          </Pressable>
        </View>
        <Text size="xxs" preset="formHelper">
          <Text size="xxs" preset="bold">
            {queueDepth}
          </Text>{" "}
          more to complete your profile
        </Text>
        <View style={$optionsContainer}>
          {questionInView && renderAnswers(questionInView)}
        </View>
        {questionInView?.allow_multi_select && (
          <Button preset="filled" onPress={handleSubmitAnswers} disabled={selectedAnswers.length === 0}>
            Submit
          </Button>
        )}
      </Screen>
    )
  },
)

const $root: ViewStyle = {
  flex: 1,
  padding:32,
}
const $titleContainer: ViewStyle = {
  flexDirection:"row",
  justifyContent:"space-between",
  alignItems:"center"
}
const $answer: ViewStyle = {
  flex: 1,
  padding:16,
  backgroundColor: colors.background,
  borderColor: colors.palette.primary400,
  borderWidth:1,
  borderRadius:8,
  marginVertical:4
}
const $answerMulti: ViewStyle = {
  flexDirection:"row",
  alignItems:"center",
  gap: 8
}

const $optionsContainer: ViewStyle = {
  marginVertical:16,
  flex:1,
  gap:16,
}
