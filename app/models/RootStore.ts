import {
  LogoModel,
  MerchantGroupModel,
  MerchantModel,
} from "app/models/Merchant/MerchantGroup"
import { ProductModel } from "app/models/Merchant/Product"
import { UserSubscriptionModel } from "app/models/UserSubscription/UserSubscription"
import { RegionModel } from "app/models/Region/Region"
import { Instance, SnapshotOut, types } from "mobx-state-tree"
import { PendingQuestionsStoreModel } from "app/models/Profile/PendingQuestions/PendingQuestions"
import { createAuthDefaultModel } from "app/models/Auth/Auth"
import { createSearchService } from "app/utils/inMemorySearch"
import { createUserStateDefaultModel } from "app/models/UserState/UserState"
import { createNotificationStoreDefaultModel } from "app/models/Notification/NotificationStore"
// @ts-ignore
export const RootStoreModel = types
  .model("RootStore")
  .props({
    auth: createAuthDefaultModel(),
    userState: createUserStateDefaultModel(),
    notifications: createNotificationStoreDefaultModel(),
    logos:  types.optional(types.array(LogoModel), []),
    merchantGroups: types.optional(types.array(MerchantGroupModel), []),
    merchants: types.optional(types.array(MerchantModel), []),
    products: types.optional(types.array(ProductModel), []),
    subscriptions: types.optional(types.array(UserSubscriptionModel), []),
    regions: types.optional(types.array(RegionModel), []),
    selectedRegion: types.maybeNull(types.safeReference(RegionModel)),
    pendingQuestions: types.optional(PendingQuestionsStoreModel, {}),
  })
  .views(self => ({
    // @ts-ignore
    get search() {
      return createSearchService(self)
    }
  }))
  .actions((self) => ({
    addProduct(product: Instance<typeof ProductModel>) {
      if (!self.products.includes(product)) {
        self.products.push(product);
      }
    },
    addMerchant(merchant: Instance<typeof MerchantModel>) {
      if (!self.merchants.includes(merchant)) {
        self.merchants.push(merchant);
      }
    },
    addMerchantGroup(merchantGroup: Instance<typeof MerchantGroupModel>) {
      if (!self.merchantGroups.includes(merchantGroup)) {
        self.merchantGroups.push(merchantGroup);
      }
    },
    addSubscription(subscription: Instance<typeof UserSubscriptionModel>) {
      if (!self.subscriptions.includes(subscription)) {
        self.subscriptions.push(subscription);
      }
    },
    addRegion(region: Instance<typeof RegionModel>) {
      if (!self.regions.includes(region)) {
        self.regions.push(region);
      }
    },
    setSelectedRegion(region: Instance<typeof RegionModel>) {
      self.selectedRegion = region;
    },
    clearNode(node: string){
      // @ts-ignore
      self[node as string].clear()
    }
  }))

// @ts-ignore
export interface RootStore extends Instance<typeof RootStoreModel> {
  auth: any
  products: any
  pendingQuestions: any
  notifications: any
}


/**
 * The data of a RootStore.
 */
export interface RootStoreSnapshot extends SnapshotOut<typeof RootStoreModel> {}

