/**
 * NotificationStore - MobX State Tree model for notification state management
 * Integrates with the notification service and provides reactive state
 */

import { Instance, SnapshotIn, SnapshotOut, types, flow } from "mobx-state-tree"
import { withSetPropAction } from "../helpers/withSetPropAction"
import {
  NotificationTarget,
  NotificationSettings,
  NotificationChannelType,
  LocalNotificationRequest
} from "app/types/notification.types"

// Lazy import to avoid circular dependencies and startup issues
const getNotificationService = () => {
  try {
    const { notificationService } = require("app/services/NotificationService")
    return notificationService
  } catch (error) {
    if (__DEV__ && console.tron) {
      console.tron.error("Failed to import notification service:", error)
    }
    return null
  }
}

/**
 * Individual notification target model
 */
export const NotificationTargetModel = types
  .model("NotificationTarget")
  .props({
    id: types.identifier,
    user_id: types.string,
    push_token: types.string,
    device_type: types.enumeration(['ios', 'android', 'web']),
    device_info: types.frozen<any>(),
    notification_preferences: types.frozen<{
      questions: boolean
      recommendations: boolean
      other: boolean
    }>(),
    is_active: types.boolean,
    created_at: types.string,
    updated_at: types.string,
    last_notification_at: types.maybeNull(types.string)
  })

/**
 * Notification settings model
 */
export const NotificationSettingsModel = types
  .model("NotificationSettings")
  .props({
    granted: types.boolean,
    ios: types.maybeNull(types.frozen<any>()),
    android: types.maybeNull(types.frozen<any>())
  })

/**
 * Main notification store model
 */
export const NotificationStoreModel = types
  .model("NotificationStore")
  .props({
    // Service state
    isInitialized: types.optional(types.boolean, false),
    isLoading: types.optional(types.boolean, false),
    lastError: types.maybeNull(types.string),

    // Permissions and token
    permissions: types.maybeNull(NotificationSettingsModel),
    pushToken: types.maybeNull(types.string),

    // User's notification targets
    targets: types.optional(types.array(NotificationTargetModel), []),

    // Notification preferences
    preferences: types.optional(types.frozen<{
      questions: boolean
      recommendations: boolean
      other: boolean
    }>(), {
      questions: true,
      recommendations: true,
      other: true
    }),

    // Statistics
    lastInitialized: types.maybeNull(types.Date),
    lastRefresh: types.maybeNull(types.Date)
  })
  .actions(withSetPropAction)
  .views((self) => ({
    /**
     * Check if notifications are properly set up
     */
    get isSetupComplete() {
      return self.isInitialized && self.permissions?.granted && self.pushToken
    },

    /**
     * Get active notification targets
     */
    get activeTargets() {
      return self.targets.filter(target => target.is_active)
    },

    /**
     * Get current device target
     */
    get currentDeviceTarget() {
      return self.targets.find(target =>
        target.push_token === self.pushToken && target.is_active
      )
    },

    /**
     * Check if specific notification type is enabled
     */
    isNotificationTypeEnabled(type: NotificationChannelType) {
      return self.preferences[type] === true
    },

    /**
     * Get notification statistics
     */
    get stats() {
      return {
        totalTargets: self.targets.length,
        activeTargets: self.activeTargets.length,
        hasPermissions: self.permissions?.granted || false,
        isSetup: this.isSetupComplete
      }
    }
  }))
  .actions((self) => ({
    /**
     * Set loading state
     */
    setLoading(loading: boolean) {
      self.isLoading = loading
    },

    /**
     * Set error state
     */
    setError(error: string | null) {
      self.lastError = error
    },

    /**
     * Clear error
     */
    clearError() {
      self.lastError = null
    },

    /**
     * Set initialization state
     */
    setInitialized(initialized: boolean) {
      self.isInitialized = initialized
      if (initialized) {
        self.lastInitialized = new Date()
      }
    },

    /**
     * Set permissions
     */
    setPermissions(permissions: NotificationSettings) {
      self.permissions = permissions
    },

    /**
     * Set push token
     */
    setPushToken(token: string | null) {
      self.pushToken = token
    },

    /**
     * Update notification preferences
     */
    updatePreferences(newPreferences: Partial<typeof self.preferences>) {
      self.preferences = { ...self.preferences, ...newPreferences }
    },

    /**
     * Set notification targets
     */
    setTargets(targets: NotificationTarget[]) {
      self.targets.clear()
      targets.forEach(target => {
        self.targets.push(target)
      })
      self.lastRefresh = new Date()
    },

    /**
     * Add or update a notification target
     */
    upsertTarget(target: NotificationTarget) {
      const existingIndex = self.targets.findIndex(t => t.id === target.id)
      if (existingIndex >= 0) {
        self.targets[existingIndex] = target
      } else {
        self.targets.push(target)
      }
    },

    /**
     * Remove a notification target
     */
    removeTarget(targetId: string) {
      const index = self.targets.findIndex(t => t.id === targetId)
      if (index >= 0) {
        self.targets.splice(index, 1)
      }
    }
  }))
  .actions((self) => ({
    /**
     * Initialize notification service
     */
    initialize: flow(function* (userId?: string) {
      self.setLoading(true)
      self.clearError()

      try {
        const notificationService = getNotificationService()
        if (!notificationService) {
          self.setError('Notification service not available')
          return false
        }

        const result = yield notificationService.initialize(userId)

        if (result.success) {
          self.setInitialized(true)
          if (result.permissions) {
            self.setPermissions(result.permissions)
          }
          if (result.pushToken) {
            self.setPushToken(result.pushToken)
          }
          return true
        } else {
          self.setError(result.error || 'Failed to initialize notifications')
          return false
        }
      } catch (error) {
        self.setError(error instanceof Error ? error.message : 'Unknown error')
        return false
      } finally {
        self.setLoading(false)
      }
    }),

    /**
     * Request notification permissions
     */
    requestPermissions: flow(function* () {
      self.setLoading(true)
      self.clearError()

      try {
        const notificationService = getNotificationService()
        if (!notificationService) {
          self.setError('Notification service not available')
          throw new Error('Notification service not available')
        }

        const permissions = yield notificationService.requestPermissions()
        self.setPermissions(permissions)
        return permissions
      } catch (error) {
        self.setError(error instanceof Error ? error.message : 'Failed to request permissions')
        throw error
      } finally {
        self.setLoading(false)
      }
    }),

    /**
     * Send local notification
     */
    sendLocalNotification: flow(function* (request: LocalNotificationRequest) {
      self.clearError()

      try {
        const notificationService = getNotificationService()
        if (!notificationService) {
          self.setError('Notification service not available')
          return null
        }

        const result = yield notificationService.sendLocalNotification(request)

        if (result.success) {
          return result.data
        } else {
          self.setError(result.error || 'Failed to send notification')
          return null
        }
      } catch (error) {
        self.setError(error instanceof Error ? error.message : 'Unknown error')
        return null
      }
    }),

    /**
     * Update notification preferences in backend
     */
    savePreferences: flow(function* (userId: string, preferences?: Partial<typeof self.preferences>) {
      const prefsToSave = preferences || self.preferences
      self.setLoading(true)
      self.clearError()

      try {
        const notificationService = getNotificationService()
        if (!notificationService) {
          self.setError('Notification service not available')
          return false
        }

        const result = yield notificationService.updateNotificationPreferences(userId, prefsToSave)

        if (result.success) {
          if (preferences) {
            self.updatePreferences(preferences)
          }
          return true
        } else {
          self.setError(result.error || 'Failed to save preferences')
          return false
        }
      } catch (error) {
        self.setError(error instanceof Error ? error.message : 'Unknown error')
        return false
      } finally {
        self.setLoading(false)
      }
    }),

    /**
     * Fetch user's notification targets
     */
    fetchTargets: flow(function* (userId: string) {
      self.setLoading(true)
      self.clearError()

      try {
        const notificationService = getNotificationService()
        if (!notificationService) {
          self.setError('Notification service not available')
          return []
        }

        const result = yield notificationService.getUserNotificationTargets(userId)

        if (result.success) {
          self.setTargets(result.data || [])
          return result.data || []
        } else {
          self.setError(result.error || 'Failed to fetch targets')
          return []
        }
      } catch (error) {
        self.setError(error instanceof Error ? error.message : 'Unknown error')
        return []
      } finally {
        self.setLoading(false)
      }
    }),

    /**
     * Register current device push token
     */
    registerPushToken: flow(function* (userId: string, deviceInfo?: any) {
      if (!self.pushToken) {
        self.setError('No push token available')
        return false
      }

      self.setLoading(true)
      self.clearError()

      try {
        const notificationService = getNotificationService()
        if (!notificationService) {
          self.setError('Notification service not available')
          return false
        }

        const result = yield notificationService.registerPushToken(userId, self.pushToken, deviceInfo)

        if (result.success && result.data) {
          self.upsertTarget(result.data)
          return true
        } else {
          self.setError(result.error || 'Failed to register push token')
          return false
        }
      } catch (error) {
        self.setError(error instanceof Error ? error.message : 'Unknown error')
        return false
      } finally {
        self.setLoading(false)
      }
    }),

    /**
     * Deactivate push token
     */
    deactivatePushToken: flow(function* (pushToken?: string) {
      const tokenToDeactivate = pushToken || self.pushToken
      if (!tokenToDeactivate) {
        self.setError('No push token to deactivate')
        return false
      }

      self.setLoading(true)
      self.clearError()

      try {
        const notificationService = getNotificationService()
        if (!notificationService) {
          self.setError('Notification service not available')
          return false
        }

        const result = yield notificationService.deactivatePushToken(tokenToDeactivate)

        if (result.success) {
          // Update local state
          const target = self.targets.find(t => t.push_token === tokenToDeactivate)
          if (target) {
            target.is_active = false
          }
          return true
        } else {
          self.setError(result.error || 'Failed to deactivate push token')
          return false
        }
      } catch (error) {
        self.setError(error instanceof Error ? error.message : 'Unknown error')
        return false
      } finally {
        self.setLoading(false)
      }
    }),

    /**
     * Refresh all notification data
     */
    refresh: flow(function* (userId: string) {
      yield self.fetchTargets(userId)
    }),

    /**
     * Reset store to initial state
     */
    reset() {
      self.isInitialized = false
      self.isLoading = false
      self.lastError = null
      self.permissions = null
      self.pushToken = null
      self.targets.clear()
      self.preferences = {
        questions: true,
        recommendations: true,
        other: true
      }
      self.lastInitialized = null
      self.lastRefresh = null
    }
  }))

export interface NotificationStore extends Instance<typeof NotificationStoreModel> {}
export interface NotificationStoreSnapshotOut extends SnapshotOut<typeof NotificationStoreModel> {}
export interface NotificationStoreSnapshotIn extends SnapshotIn<typeof NotificationStoreModel> {}

export const createNotificationStoreDefaultModel = () =>
  types.optional(NotificationStoreModel, {})
