import { Instance, SnapshotIn, SnapshotOut, types, cast } from "mobx-state-tree"
import { withSetPropAction } from "../helpers/withSetPropAction"
import { PricingTierModel, ProductModel } from "app/models/Merchant/Product" // Adjust path if needed
import { UserProfileModel } from "app/models/Profile/UserProfile"
import { HouseholdProfileModel } from "app/models/Profile/HouseholdProfile"

export const UserSubscriptionModel = types
  .model("UserSubscription")
  .props({
    id: types.identifier,
    userId: types.string,
    product: types.maybeNull(types.reference(ProductModel)),
    startedOn: types.string,
    endedOn: types.maybeNull(types.string),
    billDate: types.maybeNull(types.string),
    pricingTier: types.maybeNull(types.reference(PricingTierModel)),
  })
  .actions(withSetPropAction)
  .views((self) => ({}))
  .actions((self) => ({}))

export interface UserSubscription extends Instance<typeof UserSubscriptionModel> {}
export interface UserSubscriptionSnapshotOut extends SnapshotOut<typeof UserSubscriptionModel> {}
export interface UserSubscriptionSnapshotIn extends SnapshotIn<typeof UserSubscriptionModel> {}
export const createUserSubscriptionDefaultModel = () =>
  types.optional(UserSubscriptionModel, {
    id: '',
    userId: '',
    product: null,
    startedOn: '',
    endedOn: null,
    billDate: null,
    pricingTier: null,
  })