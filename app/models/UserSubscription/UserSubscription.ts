import { Instance, SnapshotIn, SnapshotOut, types, flow } from "mobx-state-tree"
import { withSetPropAction } from "../helpers/withSetPropAction"
import { PricingTierModel, ProductModel } from "app/models/Merchant/Product"

export const UserSubscriptionModel = types
  .model("UserSubscription")
  .props({
    id: types.identifier,
    userId: types.string,
    householdId: types.maybeNull(types.string),
    profileType: types.enumeration("ProfileType", ["user", "household"]),
    productId: types.string,
    product: types.maybeNull(types.reference(ProductModel)),
    pricingTier: types.maybeNull(types.reference(PricingTierModel)),
    startedOn: types.string,
    endedOn: types.maybeNull(types.string),
    createdAt: types.string,
    updatedAt: types.string,
  })
  .actions(withSetPropAction)
  .views((self) => ({
    get isActive() {
      return !self.endedOn || new Date(self.endedOn) > new Date()
    },
    get merchantName() {
      return self.product?.merchant?.name || "Unknown Merchant"
    },
    get merchantLogo() {
      return self.product?.merchant?.merchantGroup?.logo?.url || null
    },
    get merchantCategory() {
      const categories = self.product?.merchant?.categories
      if (categories && categories.length > 0) {
        return categories[0]
      }
      // Fallback to merchant group name or "Other"
      return self.product?.merchant?.merchantGroup?.name || "Other"
    },
    get productName() {
      return self.product?.name || "Unknown Product"
    },
    get cost() {
      return self.pricingTier?.cost || 0
    },
    get currency() {
      return self.pricingTier?.currency || "EUR"
    }
  }))

export interface UserSubscription extends Instance<typeof UserSubscriptionModel> {}
export interface UserSubscriptionSnapshotOut extends SnapshotOut<typeof UserSubscriptionModel> {}
export interface UserSubscriptionSnapshotIn extends SnapshotIn<typeof UserSubscriptionModel> {}
export const createUserSubscriptionDefaultModel = () =>
  types.optional(UserSubscriptionModel, {
    id: '',
    userId: '',
    householdId: null,
    profileType: 'user',
    productId: '',
    product: null,
    pricingTier: null,
    startedOn: '',
    endedOn: null,
    createdAt: '',
    updatedAt: '',
  })