import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree"
import { withSetPropAction } from "../helpers/withSetPropAction"


const ProfileQuestionAnswerModel = types
  .model("ProfileQuestionAnswer", {
    answer_text: types.string,
    grants_descriptors: types.optional(types.array(types.string),[]), // Default to an empty array if not provided
  });
export interface ProfileQuestionAnswer extends Instance<typeof ProfileQuestionAnswerModel> {}

export const ProfileQuestionModel = types
  .model("ProfileQuestion", {
      id: types.identifier,
    question_id: types.string,
      position: types.integer,
    target_type: types.string, // User or Household
    user_id: types.string, // ID of user
    question_text: types.string,
    allow_multi_select: types.boolean, // assuming this is a boolean, correct if needed
    options: types.array(ProfileQuestionAnswerModel), // nested array of ProfileQuestionAnswer
  })
  .actions(withSetPropAction)
  .views((self) => ({
    // Add views if needed here
  }));


export interface ProfileQuestion extends Instance<typeof ProfileQuestionModel> {}
export interface ProfileQuestionSnapshotOut extends SnapshotOut<typeof ProfileQuestionModel> {}
export interface ProfileQuestionSnapshotIn extends SnapshotIn<typeof ProfileQuestionModel> {}
export const createProfileQuestionDefaultModel = () => types.optional(ProfileQuestionModel, {
  id: "",
    userOrHousehold: "",
  targetId: "",
  questionId: "",
  questionText: "",
  allowMultiSelect: false,
  options: []
})
