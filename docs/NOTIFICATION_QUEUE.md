# Notification Queue System

A comprehensive **server-side** notification queuing mechanism for Supabase with configurable TTL (Time-To-Live) support. This system runs entirely in your Supabase backend using Edge Functions and automatically processes notifications via the Expo Push API.

## Features

- ✅ **Server-side processing** - No client-side notification sending
- ✅ Queue notifications for specific devices or all user devices
- ✅ Configurable TTL before notifications expire
- ✅ Priority-based queue processing
- ✅ Automatic retry mechanism with max attempts
- ✅ No persistent record keeping (notifications are removed after processing)
- ✅ Support for different notification channels (questions, recommendations, other)
- ✅ **Automatic processing** via Supabase Edge Functions and pg_cron
- ✅ **Expo Push API integration** for actual notification delivery
- ✅ Comprehensive error handling and logging
- ✅ Queue statistics and monitoring

## Database Schema

### Tables

#### `notification_targets`
Stores device registration information for push notifications.

```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to auth.users)
- push_token: TEXT (Device push token)
- device_type: TEXT (ios, android, web)
- device_info: JSONB (Device metadata)
- notification_preferences: JSONB (Channel preferences)
- is_active: BOOLEAN
- created_at, updated_at, last_notification_at: TIMESTAMPTZ
```

#### `notification_queue`
Temporary storage for queued notifications with TTL support.

```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to auth.users)
- target_device_id: UUID (Foreign Key to notification_targets, NULL = all devices)
- title, body: TEXT (Notification content)
- data: JSONB (Additional payload)
- channel_type: TEXT (questions, recommendations, other)
- status: TEXT (pending, processing, sent, failed, expired)
- priority: INTEGER (Higher = more priority)
- max_attempts: INTEGER (Default: 3)
- attempt_count: INTEGER
- expires_at: TIMESTAMPTZ (TTL expiration)
- scheduled_for: TIMESTAMPTZ (When to send)
- created_at, updated_at, last_attempt_at: TIMESTAMPTZ
- error_message: TEXT
```

## Database Functions

### Queue Management

#### `queue_notification_for_device()`
Queue a notification for a specific device.

```sql
SELECT queue_notification_for_device(
  p_user_id := 'user-uuid',
  p_target_device_id := 'device-uuid',
  p_title := 'Notification Title',
  p_body := 'Notification Body',
  p_data := '{"key": "value"}',
  p_channel_type := 'questions',
  p_ttl_minutes := 60,
  p_priority := 1,
  p_scheduled_for := NOW()
);
```

#### `queue_notification_for_user()`
Queue a notification for all active devices of a user.

```sql
SELECT queue_notification_for_user(
  p_user_id := 'user-uuid',
  p_title := 'Notification Title',
  p_body := 'Notification Body',
  p_data := '{"key": "value"}',
  p_channel_type := 'recommendations',
  p_ttl_minutes := 120,
  p_priority := 0,
  p_scheduled_for := NOW()
);
```

### Processing Functions

#### `get_pending_notifications()`
Retrieve notifications ready to be sent.

```sql
SELECT * FROM get_pending_notifications(100);
```

#### `mark_notification_processing()`
Mark a notification as being processed.

```sql
SELECT mark_notification_processing('notification-uuid');
```

#### `mark_notification_sent()`
Mark notification as successfully sent (removes from queue).

```sql
SELECT mark_notification_sent('notification-uuid');
```

#### `mark_notification_failed()`
Mark notification as failed (retry or remove if max attempts reached).

```sql
SELECT mark_notification_failed('notification-uuid', 'Error message');
```

### Maintenance Functions

#### `cleanup_expired_notifications()`
Remove expired notifications from the queue.

```sql
SELECT cleanup_expired_notifications();
```

#### `get_queue_statistics()`
Get queue statistics for monitoring.

```sql
SELECT * FROM get_queue_statistics();
```

## Server-Side API Usage

### Queue Notification API

Use the `/functions/v1/queue-notification` Edge Function to queue notifications from your backend:

```typescript
// Queue notification for specific device
const response = await fetch('https://your-project.supabase.co/functions/v1/queue-notification', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    user_id: 'user-123',
    target_device_id: 'device-456', // Optional - omit to send to all user devices
    title: 'Profile Question',
    body: 'Please complete your profile',
    channel_type: 'questions',
    ttl_minutes: 60,
    priority: 1,
    data: { questionId: 'q123' }
  })
})

const result = await response.json()
```

### Queue for All User Devices

```typescript
// Queue notification for all user devices (omit target_device_id)
const response = await fetch('https://your-project.supabase.co/functions/v1/queue-notification', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    user_id: 'user-123',
    title: 'New Recommendation',
    body: 'Check out this product recommendation',
    channel_type: 'recommendations',
    ttl_minutes: 120,
    priority: 0
  })
})
```

## Automatic Processing

The system automatically processes notifications using:

### 1. Supabase Edge Functions
- **`process-notification-queue`** - Processes pending notifications via Expo Push API
- **`cleanup-notification-queue`** - Removes expired notifications and resets stuck ones

### 2. Scheduled Jobs (pg_cron)
- **Every minute**: Process pending notifications
- **Every 5 minutes**: Cleanup expired notifications

### 3. Manual Processing

You can also trigger processing manually:

```bash
# Process notifications
curl -X POST 'https://your-project.supabase.co/functions/v1/process-notification-queue' \
  -H 'Authorization: Bearer YOUR_SERVICE_ROLE_KEY'

# Cleanup expired notifications
curl -X POST 'https://your-project.supabase.co/functions/v1/cleanup-notification-queue' \
  -H 'Authorization: Bearer YOUR_SERVICE_ROLE_KEY'
```

## Installation

1. **Run the migrations:**
   ```bash
   # Apply the database schema and functions
   supabase db push

   # Or run migrations individually
   psql -f supabase/migrations/20250116000001_notification_queue.sql
   psql -f supabase/migrations/20250116000002_notification_queue_functions.sql
   psql -f supabase/migrations/20250116000003_notification_queue_automation.sql
   ```

2. **Deploy Edge Functions:**
   ```bash
   # Deploy the notification processing functions
   supabase functions deploy process-notification-queue
   supabase functions deploy cleanup-notification-queue
   supabase functions deploy queue-notification
   ```

3. **Configure Environment Variables:**
   Set these in your Supabase project settings:
   ```bash
   SUPABASE_URL=your-project-url
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

4. **Update cron job URLs:**
   Edit the migration file `20250116000003_notification_queue_automation.sql` and replace `your-project-ref` with your actual Supabase project reference.

## Configuration

### TTL Settings
- Default TTL: 60 minutes
- Configurable per notification
- Automatic cleanup of expired notifications

### Retry Logic
- Default max attempts: 3
- Automatic retry for failed notifications
- Exponential backoff can be implemented in processing logic

### Priority System
- Higher numbers = higher priority
- Default priority: 0
- Processed in priority order, then FIFO

## Monitoring

Use the `get_queue_statistics()` function to monitor:
- Total pending notifications
- Total processing notifications
- Total expired notifications
- Oldest/newest pending notification timestamps

## Security

- Row Level Security (RLS) enabled on all tables
- Users can only access their own notification data
- Service role has full access for background processing
- Proper function permissions granted

## Best Practices

1. **Regular Cleanup**: Run `cleanup_expired_notifications()` periodically
2. **Monitor Queue**: Check statistics regularly to prevent queue buildup
3. **Error Handling**: Always handle service response errors
4. **TTL Planning**: Set appropriate TTL based on notification urgency
5. **Priority Usage**: Use priority sparingly for truly urgent notifications
